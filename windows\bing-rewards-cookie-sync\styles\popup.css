:root {
    --primary-color: #0078D4;
    --background-color: #f4f4f9;
    --card-background: #ffffff;
    --text-color: #333333;
    --label-color: #555555;
    --border-color: #dddddd;
    --success-color: #28a745;
    --error-color: #dc3545;
    --warn-color: #ffc107;
    --neutral-color: #6c757d;
    --font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
}

body {
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-color);
    margin: 0;
    width: 350px;
}

.container {
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.header {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo {
    width: 40px;
    height: 40px;
}

.title-section h1 {
    font-size: 1.2em;
    margin: 0;
}

.badge {
    font-size: 0.8em;
    padding: 3px 8px;
    border-radius: 12px;
    color: white;
}
.badge-success { background-color: var(--success-color); }
.badge-error { background-color: var(--error-color); }
.badge-warn { background-color: var(--warn-color); }
.badge-neutral { background-color: var(--neutral-color); }
.badge-syncing { background-color: var(--primary-color); }


.card {
    background-color: var(--card-background);
    border-radius: 8px;
    padding: 12px;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.card h2 {
    font-size: 1em;
    margin-top: 0;
    margin-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.status-grid {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 8px 12px;
    font-size: 0.9em;
    align-items: center;
}

/* 状态标签样式 */
.status-label {
    font-weight: 600;
    color: var(--label-color);
    font-size: 0.85em;
}

/* 成功相关的标签特殊样式 */
.success-label {
    color: var(--success-color);
}

/* 状态值样式 */
.status-value {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.8em;
    padding: 2px 6px;
    border-radius: 3px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
}

/* 成功时间的特殊样式 */
.success-value {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

/* 从未成功的特殊样式 */
.never-success {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
    font-style: italic;
}

.error-message {
    margin-top: 10px;
    font-size: 0.85em;
    color: var(--error-color);
    background-color: rgba(220, 53, 69, 0.1);
    border-radius: 4px;
    padding: 8px;
}

.error-message p {
    margin: 4px 0 0 0;
    word-break: break-all;
}

.button {
    width: 100%;
    padding: 10px;
    font-size: 0.9em;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    background-color: var(--label-color);
    color: white;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.button:hover {
    opacity: 0.9;
}

.button:disabled {
    background-color: var(--neutral-color);
    cursor: not-allowed;
}

.button-primary {
    background-color: var(--primary-color);
}

.button-secondary {
    background-color: var(--neutral-color);
    font-size: 0.8em;
    padding: 6px 8px;
    min-width: auto;
    width: auto;
    flex: 1;
}

.button-secondary:hover {
    background-color: #5a6268;
}

.settings-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.settings-form label {
    font-size: 0.9em;
    font-weight: 600;
    color: var(--label-color);
}

.settings-form input[type="text"],
.settings-form input[type="password"],
.settings-form input[type="number"] {
    width: calc(100% - 16px);
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.9em;
}

.api-key-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.api-key-container input {
    width: calc(100% - 16px);
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    background-color: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px;
    font-size: 0.85em;
    letter-spacing: 0.5px;
}

.api-key-container input:read-only {
    background-color: #f8f9fa;
    color: var(--text-color);
    cursor: default;
}

.api-key-container input:not(:read-only) {
    background-color: white;
    border-color: var(--primary-color);
}

.api-key-controls {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.save-status {
    text-align: center;
    font-size: 0.85em;
    color: var(--success-color);
    margin-top: 5px;
}

.hidden {
    display: none;
}

.spinner {
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top: 2px solid #fff;
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
