#!/bin/bash

# Bing Rewards Cookie Sync 部署脚本
# 用于自动化部署API服务器和数据库
#
# ⚠️  注意：此脚本用于部署自托管的API服务器版本
#
# 如果您想使用推荐的 Cloudflare Worker 版本（免费且更简单），请使用：
#   ./deploy-worker.sh
#
# Cloudflare Worker 版本的优势：
# - 完全免费（基于Cloudflare免费套餐）
# - 无需服务器维护
# - 全球CDN加速
# - 自动扩展
# - 更高的可用性
#
# 只有在您需要完全控制数据或有特殊需求时才使用此脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="bing-rewards-cookie-sync"
API_SERVER_DIR="api-server"
DATABASE_DIR="database"
BACKUP_DIR="backup"
LOG_FILE="/var/log/${PROJECT_NAME}-deploy.log"

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}"
}

print_info() {
    print_message $BLUE "$1"
}

print_success() {
    print_message $GREEN "$1"
}

print_warning() {
    print_message $YELLOW "$1"
}

print_error() {
    print_message $RED "$1"
}

# 函数：检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_error "错误: $1 命令未找到，请先安装"
        exit 1
    fi
}

# 函数：检查系统要求
check_requirements() {
    print_info "检查系统要求..."
    
    # 检查必需的命令
    check_command "node"
    check_command "npm"
    check_command "mysql"
    check_command "git"
    
    # 检查Node.js版本
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    REQUIRED_NODE_VERSION="16.0.0"
    
    if [ "$(printf '%s\n' "$REQUIRED_NODE_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_NODE_VERSION" ]; then
        print_error "Node.js版本过低，需要 >= $REQUIRED_NODE_VERSION，当前版本: $NODE_VERSION"
        exit 1
    fi
    
    print_success "系统要求检查通过"
}

# 函数：创建备份
create_backup() {
    print_info "创建备份..."
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_path="${BACKUP_DIR}/backup_${timestamp}"
    
    mkdir -p "$backup_path"
    
    # 备份API服务器
    if [ -d "$API_SERVER_DIR" ]; then
        cp -r "$API_SERVER_DIR" "$backup_path/"
        print_success "API服务器备份完成"
    fi
    
    # 备份数据库
    if [ -f ".env" ]; then
        source .env
        mysqldump -h ${DB_HOST:-*************} -u ${DB_USER:-root} -p${DB_PASSWORD} ${DB_NAME:-bing_db} > "$backup_path/database_backup.sql" 2>/dev/null || print_warning "数据库备份失败，请手动备份"
    fi
    
    print_success "备份创建完成: $backup_path"
}

# 函数：部署数据库
deploy_database() {
    print_info "部署数据库..."
    
    if [ ! -f "$DATABASE_DIR/create_tables.sql" ]; then
        print_error "数据库脚本文件不存在: $DATABASE_DIR/create_tables.sql"
        exit 1
    fi
    
    # 读取数据库配置
    if [ -f "$API_SERVER_DIR/.env" ]; then
        source "$API_SERVER_DIR/.env"
    else
        print_warning "未找到.env文件，使用默认配置"
        DB_HOST="*************"
        DB_USER="root"
        DB_NAME="bing_db"
        read -s -p "请输入MySQL密码: " DB_PASSWORD
        echo
    fi
    
    # 测试数据库连接
    print_info "测试数据库连接..."
    if mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" &>/dev/null; then
        print_success "数据库连接成功"
    else
        print_error "数据库连接失败，请检查配置"
        exit 1
    fi
    
    # 执行数据库脚本
    print_info "执行数据库脚本..."
    if mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" < "$DATABASE_DIR/create_tables.sql"; then
        print_success "数据库部署完成"
    else
        print_error "数据库脚本执行失败"
        exit 1
    fi
}

# 函数：部署API服务器
deploy_api_server() {
    print_info "部署API服务器..."
    
    cd "$API_SERVER_DIR"
    
    # 安装依赖
    print_info "安装依赖..."
    npm ci --production
    
    # 检查配置文件
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            print_warning "未找到.env文件，复制示例配置"
            cp .env.example .env
            print_warning "请编辑 $API_SERVER_DIR/.env 文件配置数据库连接"
        else
            print_error "未找到配置文件"
            exit 1
        fi
    fi
    
    # 测试API服务器
    print_info "测试API服务器..."
    timeout 10s npm start &
    SERVER_PID=$!
    sleep 5
    
    if curl -f http://localhost:3000/health &>/dev/null; then
        print_success "API服务器测试通过"
    else
        print_warning "API服务器测试失败，请检查配置"
    fi
    
    # 停止测试服务器
    kill $SERVER_PID 2>/dev/null || true
    
    cd ..
}

# 函数：配置系统服务
setup_systemd_service() {
    print_info "配置系统服务..."
    
    local service_file="/etc/systemd/system/${PROJECT_NAME}-api.service"
    local current_dir=$(pwd)
    local api_dir="$current_dir/$API_SERVER_DIR"
    
    # 创建服务文件
    sudo tee "$service_file" > /dev/null << EOF
[Unit]
Description=Bing Rewards Cookie Sync API Server
After=network.target mysql.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=$api_dir
ExecStart=/usr/bin/node server.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3000

# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=${PROJECT_NAME}-api

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$api_dir

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd
    sudo systemctl daemon-reload
    
    # 启用服务
    sudo systemctl enable "${PROJECT_NAME}-api"
    
    print_success "系统服务配置完成"
}

# 函数：配置PM2
setup_pm2() {
    print_info "配置PM2..."
    
    # 检查PM2是否安装
    if ! command -v pm2 &> /dev/null; then
        print_info "安装PM2..."
        npm install -g pm2
    fi
    
    cd "$API_SERVER_DIR"
    
    # 创建PM2配置文件
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: '${PROJECT_NAME}-api',
    script: 'server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/var/log/${PROJECT_NAME}/error.log',
    out_file: '/var/log/${PROJECT_NAME}/out.log',
    log_file: '/var/log/${PROJECT_NAME}/combined.log',
    time: true
  }]
};
EOF

    # 创建日志目录
    sudo mkdir -p "/var/log/${PROJECT_NAME}"
    sudo chown $USER:$USER "/var/log/${PROJECT_NAME}"
    
    # 启动应用
    pm2 start ecosystem.config.js
    
    # 保存PM2配置
    pm2 save
    
    # 设置开机自启
    pm2 startup
    
    cd ..
    
    print_success "PM2配置完成"
}

# 函数：配置Nginx反向代理
setup_nginx() {
    print_info "配置Nginx反向代理..."
    
    # 检查Nginx是否安装
    if ! command -v nginx &> /dev/null; then
        print_warning "Nginx未安装，跳过反向代理配置"
        return
    fi
    
    local nginx_config="/etc/nginx/sites-available/${PROJECT_NAME}"
    
    # 创建Nginx配置
    sudo tee "$nginx_config" > /dev/null << EOF
server {
    listen 80;
    server_name api.${PROJECT_NAME}.local;
    
    # 日志配置
    access_log /var/log/nginx/${PROJECT_NAME}_access.log;
    error_log /var/log/nginx/${PROJECT_NAME}_error.log;
    
    # 反向代理配置
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://localhost:3000/health;
        access_log off;
    }
}
EOF

    # 启用站点
    sudo ln -sf "$nginx_config" "/etc/nginx/sites-enabled/"
    
    # 测试配置
    if sudo nginx -t; then
        sudo systemctl reload nginx
        print_success "Nginx配置完成"
    else
        print_error "Nginx配置错误"
        exit 1
    fi
}

# 函数：验证部署
verify_deployment() {
    print_info "验证部署..."
    
    # 检查数据库
    print_info "检查数据库连接..."
    if [ -f "$API_SERVER_DIR/.env" ]; then
        source "$API_SERVER_DIR/.env"
        if mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME; SHOW TABLES;" &>/dev/null; then
            print_success "数据库连接正常"
        else
            print_error "数据库连接失败"
        fi
    fi
    
    # 检查API服务器
    print_info "检查API服务器..."
    sleep 5  # 等待服务启动
    if curl -f http://localhost:3000/health &>/dev/null; then
        print_success "API服务器运行正常"
    else
        print_error "API服务器无法访问"
    fi
    
    # 检查服务状态
    if command -v systemctl &> /dev/null; then
        if systemctl is-active --quiet "${PROJECT_NAME}-api"; then
            print_success "系统服务运行正常"
        fi
    fi
    
    if command -v pm2 &> /dev/null; then
        if pm2 list | grep -q "${PROJECT_NAME}-api"; then
            print_success "PM2服务运行正常"
        fi
    fi
}

# 函数：显示部署信息
show_deployment_info() {
    print_info "部署完成！"
    echo
    echo "=== 部署信息 ==="
    echo "项目名称: $PROJECT_NAME"
    echo "API服务器: http://localhost:3000"
    echo "健康检查: http://localhost:3000/health"
    echo "日志文件: $LOG_FILE"
    echo
    echo "=== 管理命令 ==="
    echo "查看服务状态: sudo systemctl status ${PROJECT_NAME}-api"
    echo "重启服务: sudo systemctl restart ${PROJECT_NAME}-api"
    echo "查看日志: sudo journalctl -u ${PROJECT_NAME}-api -f"
    echo
    if command -v pm2 &> /dev/null; then
        echo "PM2管理:"
        echo "  查看状态: pm2 status"
        echo "  查看日志: pm2 logs ${PROJECT_NAME}-api"
        echo "  重启服务: pm2 restart ${PROJECT_NAME}-api"
        echo
    fi
    echo "=== 下一步 ==="
    echo "1. 在Edge浏览器中加载扩展"
    echo "2. 配置扩展设置"
    echo "3. 测试Cookie同步功能"
}

# 主函数
main() {
    print_info "开始部署 $PROJECT_NAME"
    
    # 检查是否为root用户
    if [ "$EUID" -eq 0 ]; then
        print_warning "不建议以root用户运行部署脚本"
    fi
    
    # 创建日志文件
    sudo touch "$LOG_FILE"
    sudo chown $USER:$USER "$LOG_FILE"
    
    # 执行部署步骤
    check_requirements
    create_backup
    deploy_database
    deploy_api_server
    
    # 选择进程管理器
    echo
    print_info "选择进程管理器:"
    echo "1) systemd (推荐用于生产环境)"
    echo "2) PM2 (推荐用于开发环境)"
    echo "3) 跳过"
    read -p "请选择 [1-3]: " choice
    
    case $choice in
        1)
            setup_systemd_service
            ;;
        2)
            setup_pm2
            ;;
        3)
            print_info "跳过进程管理器配置"
            ;;
        *)
            print_warning "无效选择，跳过进程管理器配置"
            ;;
    esac
    
    # 询问是否配置Nginx
    echo
    read -p "是否配置Nginx反向代理? [y/N]: " setup_nginx_choice
    if [[ $setup_nginx_choice =~ ^[Yy]$ ]]; then
        setup_nginx
    fi
    
    # 验证部署
    verify_deployment
    
    # 显示部署信息
    show_deployment_info
    
    print_success "部署完成！"
}

# 错误处理
trap 'print_error "部署过程中发生错误，请检查日志: $LOG_FILE"' ERR

# 执行主函数
main "$@" 2>&1 | tee -a "$LOG_FILE"
