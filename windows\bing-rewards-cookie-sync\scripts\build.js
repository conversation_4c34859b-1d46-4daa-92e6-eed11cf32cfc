#!/usr/bin/env node

// 构建和打包脚本
// 用于准备扩展的发布版本

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const archiver = require('archiver');

class ExtensionBuilder {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.buildDir = path.join(this.projectRoot, 'build');
    this.distDir = path.join(this.projectRoot, 'dist');
    this.version = this.getVersion();
  }

  /**
   * 获取版本号
   */
  getVersion() {
    try {
      const manifestPath = path.join(this.projectRoot, 'manifest.json');
      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
      return manifest.version;
    } catch (error) {
      console.error('无法读取版本号:', error);
      return '1.0.0';
    }
  }

  /**
   * 清理构建目录
   */
  clean() {
    console.log('🧹 清理构建目录...');
    
    if (fs.existsSync(this.buildDir)) {
      fs.rmSync(this.buildDir, { recursive: true, force: true });
    }
    
    if (fs.existsSync(this.distDir)) {
      fs.rmSync(this.distDir, { recursive: true, force: true });
    }
    
    fs.mkdirSync(this.buildDir, { recursive: true });
    fs.mkdirSync(this.distDir, { recursive: true });
  }

  /**
   * 复制扩展文件
   */
  copyExtensionFiles() {
    console.log('📁 复制扩展文件...');
    
    const filesToCopy = [
      'manifest.json',
      'background.js',
      'popup.html'
    ];
    
    const dirsToCopy = [
      'modules',
      'scripts',
      'styles',
      'icons'
    ];
    
    // 复制文件
    filesToCopy.forEach(file => {
      const src = path.join(this.projectRoot, file);
      const dest = path.join(this.buildDir, file);
      
      if (fs.existsSync(src)) {
        fs.copyFileSync(src, dest);
        console.log(`  ✅ ${file}`);
      } else {
        console.warn(`  ⚠️  ${file} 不存在`);
      }
    });
    
    // 复制目录
    dirsToCopy.forEach(dir => {
      const src = path.join(this.projectRoot, dir);
      const dest = path.join(this.buildDir, dir);
      
      if (fs.existsSync(src)) {
        this.copyDirectory(src, dest);
        console.log(`  ✅ ${dir}/`);
      } else {
        console.warn(`  ⚠️  ${dir}/ 不存在`);
      }
    });
  }

  /**
   * 递归复制目录
   */
  copyDirectory(src, dest) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    
    const entries = fs.readdirSync(src, { withFileTypes: true });
    
    entries.forEach(entry => {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);
      
      if (entry.isDirectory()) {
        this.copyDirectory(srcPath, destPath);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    });
  }

  /**
   * 生成图标文件
   */
  generateIcons() {
    console.log('🎨 生成图标文件...');
    
    const iconSizes = [16, 48, 128];
    const iconsDir = path.join(this.buildDir, 'icons');
    
    if (!fs.existsSync(iconsDir)) {
      fs.mkdirSync(iconsDir, { recursive: true });
    }
    
    try {
      // 运行生成图标脚本
      console.log('  📐 运行图标生成脚本...');
      execSync('npm run generate-icons', { stdio: 'inherit' });
      
      // 复制生成的图标到构建目录
      iconSizes.forEach(size => {
        const srcPath = path.join(this.projectRoot, 'icons', `icon${size}.png`);
        const destPath = path.join(iconsDir, `icon${size}.png`);
        
        if (fs.existsSync(srcPath)) {
          fs.copyFileSync(srcPath, destPath);
          console.log(`  ✅ 已复制 icon${size}.png`);
        } else {
          console.error(`  ❌ 图标文件不存在: icon${size}.png`);
        }
      });
      
      // 复制SVG图标
      const svgSrc = path.join(this.projectRoot, 'icons', 'icon.svg');
      const svgDest = path.join(iconsDir, 'icon.svg');
      if (fs.existsSync(svgSrc)) {
        fs.copyFileSync(svgSrc, svgDest);
        console.log('  ✅ 已复制 icon.svg');
      }
    } catch (error) {
      console.error('  ❌ 生成图标时出错:', error.message);
    }
  }

  /**
   * 优化代码
   */
  optimizeCode() {
    console.log('⚡ 优化代码...');
    
    // 移除调试代码
    this.removeDebugCode();
    
    // 压缩CSS
    this.minifyCSS();
    
    // 验证JavaScript语法
    this.validateJavaScript();
  }

  /**
   * 移除调试代码
   */
  removeDebugCode() {
    const jsFiles = this.findFiles(this.buildDir, '.js');
    
    jsFiles.forEach(file => {
      let content = fs.readFileSync(file, 'utf8');
      
      // 移除console.debug调用
      content = content.replace(/console\.debug\([^)]*\);?\s*/g, '');
      
      // 移除开发环境的日志
      content = content.replace(/if\s*\(\s*process\.env\.NODE_ENV\s*===\s*['"]development['"]\s*\)\s*{[^}]*}/g, '');
      
      fs.writeFileSync(file, content);
    });
  }

  /**
   * 压缩CSS
   */
  minifyCSS() {
    const cssFiles = this.findFiles(this.buildDir, '.css');
    
    cssFiles.forEach(file => {
      let content = fs.readFileSync(file, 'utf8');
      
      // 简单的CSS压缩
      content = content
        .replace(/\/\*[^*]*\*+(?:[^/*][^*]*\*+)*\//g, '') // 移除注释
        .replace(/\s+/g, ' ') // 压缩空白
        .replace(/;\s*}/g, '}') // 移除最后的分号
        .trim();
      
      fs.writeFileSync(file, content);
    });
  }

  /**
   * 验证JavaScript语法
   */
  validateJavaScript() {
    const jsFiles = this.findFiles(this.buildDir, '.js');
    
    jsFiles.forEach(file => {
      try {
        const content = fs.readFileSync(file, 'utf8');
        // 简单的语法检查
        new Function(content);
        console.log(`  ✅ ${path.relative(this.buildDir, file)}`);
      } catch (error) {
        console.error(`  ❌ ${path.relative(this.buildDir, file)}: ${error.message}`);
      }
    });
  }

  /**
   * 查找指定扩展名的文件
   */
  findFiles(dir, ext) {
    const files = [];
    
    const scan = (currentDir) => {
      const entries = fs.readdirSync(currentDir, { withFileTypes: true });
      
      entries.forEach(entry => {
        const fullPath = path.join(currentDir, entry.name);
        
        if (entry.isDirectory()) {
          scan(fullPath);
        } else if (entry.name.endsWith(ext)) {
          files.push(fullPath);
        }
      });
    };
    
    scan(dir);
    return files;
  }

  /**
   * 验证扩展
   */
  validateExtension() {
    console.log('🔍 验证扩展...');
    
    // 检查必需文件
    const requiredFiles = [
      'manifest.json',
      'background.js',
      'popup.html'
    ];
    
    const missingFiles = requiredFiles.filter(file => {
      return !fs.existsSync(path.join(this.buildDir, file));
    });
    
    if (missingFiles.length > 0) {
      console.error('❌ 缺少必需文件:', missingFiles);
      return false;
    }
    
    // 验证manifest.json
    try {
      const manifestPath = path.join(this.buildDir, 'manifest.json');
      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
      
      if (manifest.manifest_version !== 3) {
        console.error('❌ 不是Manifest V3格式');
        return false;
      }
      
      console.log('  ✅ Manifest V3格式正确');
      console.log(`  ✅ 版本: ${manifest.version}`);
      console.log(`  ✅ 名称: ${manifest.name}`);
      
    } catch (error) {
      console.error('❌ manifest.json格式错误:', error.message);
      return false;
    }
    
    return true;
  }

  /**
   * 创建ZIP包
   */
  createZipPackage() {
    console.log('📦 创建ZIP包...');
    
    return new Promise((resolve, reject) => {
      const zipPath = path.join(this.distDir, `bing-rewards-cookie-sync-v${this.version}.zip`);
      const output = fs.createWriteStream(zipPath);
      const archive = archiver('zip', { zlib: { level: 9 } });
      
      output.on('close', () => {
        console.log(`  ✅ 创建成功: ${zipPath}`);
        console.log(`  📊 大小: ${(archive.pointer() / 1024 / 1024).toFixed(2)} MB`);
        resolve(zipPath);
      });
      
      archive.on('error', (err) => {
        console.error('❌ 创建ZIP包失败:', err);
        reject(err);
      });
      
      archive.pipe(output);
      archive.directory(this.buildDir, false);
      archive.finalize();
    });
  }

  /**
   * 生成发布说明
   */
  generateReleaseNotes() {
    console.log('📝 生成发布说明...');
    
    const releaseNotes = `# Bing Rewards Cookie Sync v${this.version}

## 🚀 新功能
- 自动同步Bing Rewards Cookie数据
- 安全的数据传输和存储
- 完善的错误处理和日志记录
- 直观的用户界面

## 🔧 技术特性
- Manifest V3兼容
- AES-GCM数据加密
- HMAC-SHA256数字签名
- MySQL数据库支持

## 📋 系统要求
- Microsoft Edge 88+
- MySQL 5.7+
- Node.js 16+ (API服务器)

## 🛠️ 安装方法
1. 下载扩展包
2. 解压到本地目录
3. 在Edge中加载解压缩的扩展
4. 配置API服务器和数据库

## 📞 技术支持
- GitHub: https://github.com/alxxxxla/bing-rewards-cookie-sync
- 维护者: @alxxxxla

---
构建时间: ${new Date().toISOString()}
构建版本: v${this.version}
`;
    
    const notesPath = path.join(this.distDir, `RELEASE-NOTES-v${this.version}.md`);
    fs.writeFileSync(notesPath, releaseNotes);
    
    console.log(`  ✅ 发布说明: ${notesPath}`);
  }

  /**
   * 生成校验和
   */
  generateChecksums() {
    console.log('🔐 生成校验和...');
    
    const crypto = require('crypto');
    const zipPath = path.join(this.distDir, `bing-rewards-cookie-sync-v${this.version}.zip`);
    
    if (fs.existsSync(zipPath)) {
      const content = fs.readFileSync(zipPath);
      const sha256 = crypto.createHash('sha256').update(content).digest('hex');
      const md5 = crypto.createHash('md5').update(content).digest('hex');
      
      const checksums = `# 校验和 - Bing Rewards Cookie Sync v${this.version}

## SHA256
${sha256}

## MD5
${md5}

## 验证方法

### Windows (PowerShell)
\`\`\`powershell
Get-FileHash -Algorithm SHA256 bing-rewards-cookie-sync-v${this.version}.zip
\`\`\`

### macOS/Linux
\`\`\`bash
sha256sum bing-rewards-cookie-sync-v${this.version}.zip
\`\`\`
`;
      
      const checksumPath = path.join(this.distDir, `CHECKSUMS-v${this.version}.md`);
      fs.writeFileSync(checksumPath, checksums);
      
      console.log(`  ✅ SHA256: ${sha256}`);
      console.log(`  ✅ MD5: ${md5}`);
      console.log(`  ✅ 校验和文件: ${checksumPath}`);
    }
  }

  /**
   * 执行完整构建
   */
  async build() {
    console.log(`🚀 开始构建 Bing Rewards Cookie Sync v${this.version}`);
    console.log('='.repeat(60));
    
    try {
      // 1. 清理
      this.clean();
      
      // 2. 复制文件
      this.copyExtensionFiles();
      
      // 3. 生成图标
      this.generateIcons();
      
      // 4. 优化代码
      this.optimizeCode();
      
      // 5. 验证扩展
      if (!this.validateExtension()) {
        throw new Error('扩展验证失败');
      }
      
      // 6. 创建ZIP包
      await this.createZipPackage();
      
      // 7. 生成发布说明
      this.generateReleaseNotes();
      
      // 8. 生成校验和
      this.generateChecksums();
      
      console.log('='.repeat(60));
      console.log('🎉 构建完成！');
      console.log(`📦 输出目录: ${this.distDir}`);
      console.log(`📋 构建目录: ${this.buildDir}`);
      
    } catch (error) {
      console.error('❌ 构建失败:', error.message);
      process.exit(1);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const builder = new ExtensionBuilder();
  builder.build();
}

module.exports = ExtensionBuilder;
