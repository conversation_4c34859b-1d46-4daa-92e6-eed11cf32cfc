// Bing Rewards <PERSON>ie Sync - Background Service Worker
// 后台服务脚本，处理Cookie获取和定时同步

// 简化的日志函数
function log(level, message, data = null) {
  const timestamp = new Date().toLocaleTimeString();
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;

  switch (level.toLowerCase()) {
    case 'error':
      console.error(logMessage, data);
      break;
    case 'warn':
      console.warn(logMessage, data);
      break;
    case 'info':
      console.info(logMessage, data);
      break;
    case 'debug':
      console.debug(logMessage, data);
      break;
    default:
      console.log(logMessage, data);
  }
}

// Cookie获取函数
async function getCookies(url) {
  try {
    // 尝试多个域名
    const domains = [
      'bing.com',
      '.bing.com',
      'www.bing.com',
      'rewards.bing.com'
    ];

    let allCookies = [];

    for (const domain of domains) {
      try {
        const domainCookies = await chrome.cookies.getAll({ domain: domain });
        if (domainCookies.length > 0) {
          allCookies.push(...domainCookies);
        }
      } catch (error) {
        console.error(`获取域名 ${domain} Cookie失败:`, error);
      }
    }

    // 去重
    const uniqueCookies = allCookies.filter((cookie, index, self) =>
      index === self.findIndex(c => c.name === cookie.name && c.domain === cookie.domain)
    );

    if (uniqueCookies.length === 0) {
      return [];
    }

    // 过滤相关Cookie
    const relevantNames = [
      'MUID', 'MUIDB', '_EDGE_V', '_EDGE_S', 'SRCHD', 'SRCHUID',
      'SRCHUSR', 'SRCHHPGUSR', '_RwBf', 'ANON', 'NAP', 'PPLState',
      'KievRPSSecAuth', 'WLSSC', 'MSPRequ', 'MSPSoft', 'MSPBack',
      'MSPProf', 'MSPPre', 'MSCC', 'ai_user', 'ai_session'
    ];

    const relevantCookies = uniqueCookies.filter(cookie => {
      return relevantNames.some(name =>
        cookie.name.toLowerCase().includes(name.toLowerCase())
      ) || cookie.name.includes('MUID') || cookie.name.includes('_EDGE') || cookie.domain.includes('bing');
    });

    // 如果没有相关Cookie但有Bing Cookie，返回所有Bing Cookie
    if (relevantCookies.length === 0 && uniqueCookies.length > 0) {
      return uniqueCookies;
    }

    return relevantCookies;

  } catch (error) {
    console.error('获取Cookie失败:', error);
    return [];
  }
}

// Cookie验证函数
function validateCookiesForUpload(cookies) {
  // 检查是否包含必需的 .AspNetCore.Antiforgery.icPscOZlg04 Cookie
  const requiredCookieName = '.AspNetCore.Antiforgery.icPscOZlg04';

  const hasRequiredCookie = cookies.some(cookie =>
    cookie.name === requiredCookieName
  );

  if (!hasRequiredCookie) {
    return {
      valid: false,
      error: `缺少必需的认证Cookie: ${requiredCookieName}`,
      details: '请确保您已正确登录Bing.com并完成必要的认证步骤。'
    };
  }

  return {
    valid: true,
    error: null,
    details: null
  };
}

const BING_URL = 'https://www.bing.com';
const ALARM_NAME = 'cookieSyncAlarm';

// Function to sync cookies to the remote Cloudflare Worker
async function syncCookies(isManualSync = false) {
  // Immediately set status to syncing to give instant feedback to the user
  await chrome.storage.local.set({ lastSyncStatus: 'syncing', lastSyncTime: Date.now() });
  log('info', `Starting cookie sync... (${isManualSync ? 'manual' : 'automatic'})`);
  try {
    const config = await chrome.storage.local.get(['apiUrl', 'apiKey', 'autoSync']);

    // Only check autoSync for automatic syncs, not manual ones
    if (!isManualSync && !config.autoSync) {
      log('info', 'Auto-sync is disabled. Skipping automatic sync.');
      await chrome.storage.local.set({ lastSyncStatus: 'none', lastSyncTime: Date.now() });
      return;
    }

    if (!config.apiUrl || !config.apiKey) {
      const errorMsg = 'API URL 或 API Key 未配置。请在扩展设置中配置这些信息。';
      log('error', errorMsg, { apiUrl: !!config.apiUrl, apiKey: !!config.apiKey });
      // Set status to error before returning
      await chrome.storage.local.set({
        lastSyncStatus: 'error',
        lastSyncTime: Date.now(),
        lastError: errorMsg
      });
      await chrome.action.setBadgeText({ text: 'CFG' });
      await chrome.action.setBadgeBackgroundColor({ color: '#FF9800' });
      return;
    }

    const cookies = await getCookies(BING_URL);
    if (cookies.length === 0) {
      const warnMsg = '未找到Bing Cookie。请确保您已登录Bing.com。';
      log('warn', warnMsg, { url: BING_URL });
      // It's not an error if cookies are not present, maybe the user is logged out.
      // We can set a neutral or specific badge.
      await chrome.action.setBadgeText({ text: 'NO' });
      await chrome.action.setBadgeBackgroundColor({ color: '#757575' });
      await chrome.storage.local.set({
        lastSyncStatus: 'no_cookies',
        lastSyncTime: Date.now(),
        lastError: warnMsg
      });
      return;
    }

    // 验证Cookie是否包含必需的认证项
    const validation = validateCookiesForUpload(cookies);
    if (!validation.valid) {
      const errorMsg = `Cookie验证失败: ${validation.error}`;
      log('error', errorMsg, {
        details: validation.details,
        cookieCount: cookies.length,
        cookieNames: cookies.map(c => c.name)
      });

      await chrome.storage.local.set({
        lastSyncStatus: 'error',
        lastSyncTime: Date.now(),
        lastError: `${errorMsg}\n${validation.details}`
      });
      await chrome.action.setBadgeText({ text: 'ERR' });
      await chrome.action.setBadgeBackgroundColor({ color: '#F44336' });
      return;
    }

    const cookieString = cookies.map(c => `${c.name}=${c.value}`).join('; ');
    const fullUrl = `${config.apiUrl}?token=${config.apiKey}`;

    const response = await fetch(fullUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'text/plain',
        'User-Agent': 'Bing-Rewards-Cookie-Sync/2.0'
      },
      body: cookieString,
      mode: 'cors'
    });

    if (!response.ok) {
      const errorBody = await response.text();
      const errorMsg = `API请求失败 (状态码: ${response.status})`;
      const detailedError = errorBody ? `${errorMsg}: ${errorBody}` : errorMsg;
      throw new Error(detailedError);
    }

    const responseText = await response.text();
    log('info', `Cookie同步成功`, {
      cookieCount: cookies.length,
      response: responseText,
      syncType: isManualSync ? 'manual' : 'automatic'
    });
    const currentTime = Date.now();
    await chrome.storage.local.set({
      lastSyncStatus: 'success',
      lastSyncTime: currentTime,
      lastSuccessTime: currentTime, // 同步成功时同时更新成功时间
      lastError: ''
    });
    await chrome.action.setBadgeText({ text: 'OK' });
    await chrome.action.setBadgeBackgroundColor({ color: '#4CAF50' });

  } catch (error) {
    const errorMsg = `Cookie同步失败: ${error.message}`;
    log('error', errorMsg, {
      error: error.stack,
      syncType: isManualSync ? 'manual' : 'automatic',
      timestamp: new Date().toISOString()
    });
    await chrome.storage.local.set({
      lastSyncStatus: 'error',
      lastSyncTime: Date.now(),
      lastError: errorMsg
      // 注意：失败时不更新 lastSuccessTime
    });
    await chrome.action.setBadgeText({ text: 'ERR' });
    await chrome.action.setBadgeBackgroundColor({ color: '#F44336' });
  }
}

// --- Event Listeners ---

/**
 * Resets the sync status if it was stuck in 'syncing' state from a previous session.
 */
async function resetStuckSyncStatus() {
    const { lastSyncStatus } = await chrome.storage.local.get('lastSyncStatus');
    if (lastSyncStatus === 'syncing') {
        const errorMsg = '同步过程意外中断，状态已重置。';
        log('warn', errorMsg);
        await chrome.storage.local.set({ lastSyncStatus: 'error', lastError: errorMsg });
    }
}

// Listener for when the extension is first installed or updated
chrome.runtime.onInstalled.addListener(() => {
    log('info', 'Extension installed or updated.');
    resetStuckSyncStatus(); // Reset status on update/install
    // Set default values on installation
    chrome.storage.local.set({
        autoSync: true,
        syncInterval: 5, // Default to 5 minutes
        intervalUnit: 'minutes',
        apiUrl: '',
        apiKey: '',
        lastSyncStatus: 'none',
        lastSyncTime: null,
        lastSuccessTime: null, // 添加成功时间字段
        lastError: ''
    });
    // Start the alarm to run the sync periodically
    setupAlarm();
});

// Also reset status on browser startup
chrome.runtime.onStartup.addListener(() => {
    log('info', 'Browser startup detected.');
    resetStuckSyncStatus();
});

// Listener for the alarm
chrome.alarms.onAlarm.addListener((alarm) => {
  if (alarm.name === ALARM_NAME) {
    log('info', 'Alarm triggered. Running scheduled sync.');
    syncCookies();
  }
});

// Listener for messages from the popup or other parts of the extension
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'syncNow') {
    log('info', 'Manual sync requested from popup.');

    // 立即发送响应，不等待同步完成
    sendResponse({ status: 'Sync initiated' });

    // 异步执行同步操作
    syncCookies(true).catch(error => {
      log('error', 'Manual sync failed:', error);
    });

    return false; // 同步响应
  }
  if (request.action === 'updateSettings') {
    log('info', 'Settings updated. Re-evaluating alarm.');
    setupAlarm();
    sendResponse({ status: 'Settings applied' });
  }
  if (request.action === 'debugAlarm') {
    log('info', 'Debug alarm status requested.');
    debugAlarmStatus().then(() => sendResponse({ status: 'Debug completed' }));
    return true;
  }
});

// Function to create or update the sync alarm based on settings
async function setupAlarm() {
    try {
        const { autoSync, syncInterval, intervalUnit } = await chrome.storage.local.get(['autoSync', 'syncInterval', 'intervalUnit']);

        // 首先检查现有的定时器
        const existingAlarm = await chrome.alarms.get(ALARM_NAME);

        if (autoSync) {
            let interval = syncInterval || 5; // Default to 5 minutes if not set

            // 向后兼容：检测是否为旧的小时值
            if (interval <= 24 && !intervalUnit) {
                // 可能是旧的小时值，转换为分钟
                interval = interval * 60;
                log('info', `Detected legacy hour value, converted to minutes: ${interval}`);

                // 保存转换后的值和单位标记
                await chrome.storage.local.set({
                    syncInterval: interval,
                    intervalUnit: 'minutes'
                });
            }

            const periodInMinutes = interval;

            // 检查是否需要更新定时器
            let needsUpdate = false;
            if (!existingAlarm) {
                needsUpdate = true;
                log('info', 'No existing alarm found, creating new one.');
            } else if (existingAlarm.periodInMinutes !== periodInMinutes) {
                needsUpdate = true;
                log('info', `Alarm interval changed from ${existingAlarm.periodInMinutes} to ${periodInMinutes} minutes.`);
            }

            if (needsUpdate) {
                // 清除现有定时器
                await chrome.alarms.clear(ALARM_NAME);

                // 创建新的定时器
                chrome.alarms.create(ALARM_NAME, {
                    delayInMinutes: 1, // Run first time after 1 minute
                    periodInMinutes: periodInMinutes,
                });
                log('info', `Sync alarm set to run every ${interval} minutes.`);
            } else {
                log('info', `Sync alarm already configured correctly for ${interval} minutes.`);
            }
        } else {
            // 自动同步已禁用，清除定时器
            if (existingAlarm) {
                await chrome.alarms.clear(ALARM_NAME);
                log('info', 'Auto-sync disabled, alarm cleared.');
            } else {
                log('info', 'Auto-sync disabled, no alarm to clear.');
            }
        }
    } catch (error) {
        log('error', 'Error setting up alarm:', error);
    }
}

// Debug function to check alarm status
async function debugAlarmStatus() {
    try {
        const alarms = await chrome.alarms.getAll();
        const syncAlarm = alarms.find(alarm => alarm.name === ALARM_NAME);

        log('info', 'Debug: All alarms:', alarms);

        if (syncAlarm) {
            const nextRun = new Date(syncAlarm.scheduledTime);
            const intervalMinutes = syncAlarm.periodInMinutes;
            log('info', `Debug: Sync alarm found - Next run: ${nextRun.toLocaleString()}, Interval: ${intervalMinutes} minutes`);
        } else {
            log('info', 'Debug: No sync alarm found');
        }

        const { autoSync, syncInterval } = await chrome.storage.local.get(['autoSync', 'syncInterval']);
        log('info', `Debug: Settings - autoSync: ${autoSync}, syncInterval: ${syncInterval} minutes`);

    } catch (error) {
        log('error', 'Debug: Error checking alarm status:', error);
    }
}



// Initial setup on browser start
log('info', 'Background script loaded.');
setupAlarm();

// 延迟执行调试检查，确保初始化完成
setTimeout(() => {
    debugAlarmStatus();
}, 2000);
