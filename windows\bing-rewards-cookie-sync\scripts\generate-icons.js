// 生成图标脚本
// 此脚本使用sharp库将SVG图标转换为不同尺寸的PNG图标

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

const ICON_SIZES = [16, 48, 128];
const SVG_SOURCE = path.join(__dirname, '../icons/icon.svg');
const OUTPUT_DIR = path.join(__dirname, '../icons');

async function generateIcons() {
  console.log('开始生成图标...');
  
  // 确保SVG源文件存在
  if (!fs.existsSync(SVG_SOURCE)) {
    console.error('错误: SVG源文件不存在:', SVG_SOURCE);
    process.exit(1);
  }
  
  // 确保输出目录存在
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }
  
  // 读取SVG源文件
  const svgBuffer = fs.readFileSync(SVG_SOURCE);
  
  // 为每个尺寸生成PNG图标
  for (const size of ICON_SIZES) {
    const outputPath = path.join(OUTPUT_DIR, `icon${size}.png`);
    console.log(`生成 ${size}x${size} 图标...`);
    
    try {
      await sharp(svgBuffer)
        .resize(size, size)
        .png()
        .toFile(outputPath);
      
      console.log(`成功: ${outputPath}`);
    } catch (err) {
      console.error(`生成 ${size}x${size} 图标时出错:`, err);
    }
  }
  
  console.log('图标生成完成！');
}

// 执行生成
generateIcons().catch(err => {
  console.error('图标生成失败:', err);
  process.exit(1);
}); 