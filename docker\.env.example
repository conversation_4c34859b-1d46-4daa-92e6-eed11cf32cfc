# Bing Rewards 自动化脚本环境变量配置示例
# 维护者: alxxxxla
# 复制此文件为 .env 并修改相应配置

# ===========================================
# 钉钉通知配置
# ===========================================
# 钉钉机器人 Webhook URL
DINGDING_WEBHOOK=https://oapi.dingtalk.com/robot/send?access_token=your_token_here

# 钉钉机器人密钥（如果启用了加签验证）
DINGDING_SECRET=your_secret_here

# ===========================================
# Cookie 获取配置
# ===========================================
# Cookie 获取 URL（从插件同步的 URL）
COOKIE_URL=https://bing-cookie-sync-worker.5s6.com/?token=

# ===========================================
# 搜索任务配置
# ===========================================
# PC端每小时最大搜索次数
PC_MAX_PER_HOUR=4

# 移动端每小时最大搜索次数
MOBILE_MAX_PER_HOUR=3

# PC端每日最大搜索次数
PC_MAX_DAILY=90

# 移动端每日最大搜索次数
MOBILE_MAX_DAILY=60

# ===========================================
# 工作时间配置
# ===========================================
# 开始工作时间（24小时制）
WORKING_HOURS_START=6

# 结束工作时间（24小时制）
WORKING_HOURS_END=21

# ===========================================
# 奖励任务配置
# ===========================================
# 奖励任务执行时间点（逗号分隔，24小时制）
REWARD_EXECUTION_TIMES=10,13,20

# ===========================================
# 运行模式配置
# ===========================================
# 运行模式：single（单次运行）或 schedule（定时运行）
RUN_MODE=single

# 定时运行间隔（分钟）
SCHEDULE_INTERVAL=60

# ===========================================
# 系统配置
# ===========================================
# 时区设置
TZ=Asia/Shanghai

# 日志级别：DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# ===========================================
# 高级配置
# ===========================================
# Cookie验证最大尝试次数
VALIDATION_MAX_ATTEMPTS=3

# Cookie验证重试等待时间（秒）
VALIDATION_RETRY_WAIT_MIN=5
VALIDATION_RETRY_WAIT_MAX=10

# 搜索间隔配置（秒）
PC_SEARCH_INTERVAL_MIN=5
PC_SEARCH_INTERVAL_MAX=12
MOBILE_SEARCH_INTERVAL_MIN=8
MOBILE_SEARCH_INTERVAL_MAX=15

# 设备切换间隔（秒）
DEVICE_SWITCH_INTERVAL_MIN=15
DEVICE_SWITCH_INTERVAL_MAX=30

# ===========================================
# Docker 特定配置
# ===========================================
# 容器重启策略：no, always, unless-stopped, on-failure
RESTART_POLICY=unless-stopped

# 内存限制（如：256m, 512m, 1g）
MEMORY_LIMIT=256m

# CPU 限制（如：0.5, 1.0）
CPU_LIMIT=0.5
