# Bing Rewards Extension 安装助手 (PowerShell版本)
# 维护者: alxxxxla
# 版本: 2.0

param(
    [switch]$Force,
    [switch]$Quiet
)

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 扩展配置
$ExtensionId = "mplajoflljljogjfpilcocljdgjbgkla"
$ExtensionName = "Bing Rewards Cookie Sync"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    if (-not $Quiet) {
        Write-Host $Message -ForegroundColor $Color
    }
}

# 检查管理员权限
function Test-AdminRights {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 创建注册表项
function New-RegistryPath {
    param([string]$Path)
    
    try {
        if (-not (Test-Path $Path)) {
            New-Item -Path $Path -Force | Out-Null
            Write-ColorOutput "✓ 创建注册表路径: $Path" "Green"
        } else {
            Write-ColorOutput "✓ 注册表路径已存在: $Path" "Yellow"
        }
        return $true
    } catch {
        Write-ColorOutput "✗ 创建注册表路径失败: $Path" "Red"
        Write-ColorOutput "  错误: $($_.Exception.Message)" "Red"
        return $false
    }
}

# 设置注册表值
function Set-RegistryValue {
    param(
        [string]$Path,
        [string]$Name,
        [object]$Value,
        [string]$Type = "String"
    )
    
    try {
        Set-ItemProperty -Path $Path -Name $Name -Value $Value -Type $Type -Force
        Write-ColorOutput "✓ 设置注册表值: $Path\$Name = $Value" "Green"
        return $true
    } catch {
        Write-ColorOutput "✗ 设置注册表值失败: $Path\$Name" "Red"
        Write-ColorOutput "  错误: $($_.Exception.Message)" "Red"
        return $false
    }
}

# 删除注册表项
function Remove-RegistryPath {
    param([string]$Path)
    
    try {
        if (Test-Path $Path) {
            Remove-Item -Path $Path -Recurse -Force
            Write-ColorOutput "✓ 删除限制性注册表项: $Path" "Green"
        }
        return $true
    } catch {
        Write-ColorOutput "⚠ 删除注册表项时出现警告: $Path" "Yellow"
        return $false
    }
}

# 检查Edge是否运行
function Test-EdgeRunning {
    $edgeProcesses = Get-Process -Name "msedge" -ErrorAction SilentlyContinue
    return ($edgeProcesses.Count -gt 0)
}

# 主安装函数
function Install-Extension {
    Write-ColorOutput "===============================================" "Cyan"
    Write-ColorOutput "    $ExtensionName 安装助手" "Cyan"
    Write-ColorOutput "    维护者: alxxxxla" "Cyan"
    Write-ColorOutput "===============================================" "Cyan"
    Write-ColorOutput ""

    # 检查管理员权限
    if (Test-AdminRights) {
        Write-ColorOutput "[✓] 检测到管理员权限" "Green"
    } else {
        Write-ColorOutput "[!] 警告：未检测到管理员权限" "Yellow"
        Write-ColorOutput "    某些注册表操作可能需要管理员权限" "Yellow"
        Write-ColorOutput "    建议右键选择'以管理员身份运行'" "Yellow"
        Write-ColorOutput ""
        
        if (-not $Force) {
            $continue = Read-Host "是否继续？(Y/N)"
            if ($continue -ne "Y" -and $continue -ne "y") {
                Write-ColorOutput "安装已取消" "Red"
                return $false
            }
        }
    }

    # 检查Edge是否运行
    if (Test-EdgeRunning) {
        Write-ColorOutput "[!] 检测到 Microsoft Edge 正在运行" "Yellow"
        Write-ColorOutput "    建议关闭 Edge 浏览器以确保配置生效" "Yellow"
        Write-ColorOutput ""
        
        if (-not $Force) {
            $continue = Read-Host "是否继续？(Y/N)"
            if ($continue -ne "Y" -and $continue -ne "y") {
                Write-ColorOutput "安装已取消" "Red"
                return $false
            }
        }
    }

    $success = $true

    # 步骤1：创建注册表路径
    Write-ColorOutput "[1/6] 正在配置注册表路径..." "Blue"
    $paths = @(
        "HKCU:\Software\Policies\Microsoft\Edge",
        "HKCU:\Software\Policies\Microsoft\Edge\ExtensionInstallAllowlist",
        "HKCU:\Software\Microsoft\Edge\Extensions"
    )
    
    foreach ($path in $paths) {
        $success = $success -and (New-RegistryPath -Path $path)
    }

    # 步骤2：启用扩展安装
    Write-ColorOutput "[2/6] 启用扩展安装权限..." "Blue"
    $success = $success -and (Set-RegistryValue -Path "HKCU:\Software\Policies\Microsoft\Edge" -Name "AllowInstallingExtensions" -Value 1 -Type "DWord")

    # 步骤3：添加扩展白名单
    Write-ColorOutput "[3/6] 将扩展加入白名单..." "Blue"
    $success = $success -and (Set-RegistryValue -Path "HKCU:\Software\Policies\Microsoft\Edge\ExtensionInstallAllowlist" -Name "1" -Value $ExtensionId -Type "String")

    # 步骤4：启用开发者模式
    Write-ColorOutput "[4/6] 启用开发者模式..." "Blue"
    $success = $success -and (Set-RegistryValue -Path "HKCU:\Software\Microsoft\Edge\Extensions" -Name "DeveloperMode" -Value 1 -Type "DWord")

    # 步骤5：清理限制设置
    Write-ColorOutput "[5/6] 清理可能的限制设置..." "Blue"
    Remove-RegistryPath -Path "HKCU:\Software\Policies\Microsoft\Edge\ExtensionInstallBlocklist"
    Remove-RegistryPath -Path "HKCU:\Software\Policies\Microsoft\Edge\ExtensionInstallForcelist"

    # 步骤6：完成
    Write-ColorOutput "[6/6] 配置完成！" "Blue"
    Write-ColorOutput ""

    if ($success) {
        Write-ColorOutput "===============================================" "Green"
        Write-ColorOutput "    配置成功完成！" "Green"
        Write-ColorOutput "===============================================" "Green"
    } else {
        Write-ColorOutput "===============================================" "Red"
        Write-ColorOutput "    配置过程中出现错误！" "Red"
        Write-ColorOutput "===============================================" "Red"
        return $false
    }

    return $true
}

# 显示安装说明
function Show-InstallInstructions {
    Write-ColorOutput ""
    Write-ColorOutput "📋 扩展安装步骤：" "Cyan"
    Write-ColorOutput "   1. 打开 Microsoft Edge 浏览器" "White"
    Write-ColorOutput "   2. 访问 edge://extensions/" "White"
    Write-ColorOutput "   3. 确保右上角'开发人员模式'已开启" "White"
    Write-ColorOutput "   4. 选择以下任一方式安装：" "White"
    Write-ColorOutput ""
    Write-ColorOutput "🔧 方式一：拖拽安装（推荐）" "Yellow"
    Write-ColorOutput "   - 将 bing-rewards-cookie-sync.crx 文件" "White"
    Write-ColorOutput "   - 直接拖拽到扩展页面" "White"
    Write-ColorOutput ""
    Write-ColorOutput "🔧 方式二：解压安装" "Yellow"
    Write-ColorOutput "   - 点击'加载解压缩的扩展'" "White"
    Write-ColorOutput "   - 选择 bing-rewards-cookie-sync 文件夹" "White"
    Write-ColorOutput ""
    Write-ColorOutput "⚠️  注意事项：" "Red"
    Write-ColorOutput "   - 如果安装失败，请重启 Edge 浏览器后重试" "White"
    Write-ColorOutput "   - 某些企业版 Edge 可能有额外限制" "White"
    Write-ColorOutput "   - 安装成功后扩展图标会出现在工具栏" "White"
    Write-ColorOutput ""
}

# 主程序
try {
    if (Install-Extension) {
        Show-InstallInstructions
        
        if (-not $Quiet) {
            $openExtensions = Read-Host "是否立即打开 Edge 扩展页面？(Y/N)"
            if ($openExtensions -eq "Y" -or $openExtensions -eq "y") {
                Write-ColorOutput "正在打开 Edge 扩展页面..." "Green"
                Start-Process "msedge.exe" -ArgumentList "edge://extensions/"
            }
        }
        
        Write-ColorOutput ""
        Write-ColorOutput "===============================================" "Cyan"
        Write-ColorOutput "    脚本执行完毕" "Cyan"
        Write-ColorOutput "    如有问题请联系维护者: alxxxxla" "Cyan"
        Write-ColorOutput "===============================================" "Cyan"
    } else {
        Write-ColorOutput "安装失败，请检查错误信息并重试" "Red"
        exit 1
    }
} catch {
    Write-ColorOutput "脚本执行过程中发生未预期的错误：" "Red"
    Write-ColorOutput $_.Exception.Message "Red"
    exit 1
}

if (-not $Quiet) {
    Read-Host "按任意键退出"
}
