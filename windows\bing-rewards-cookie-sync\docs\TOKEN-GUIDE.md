# 🔑 TOKEN使用机制详细说明

## 📋 概述

本文档详细说明Bing Rewards Cookie Sync项目中TOKEN的使用机制、配置方法和安全建议。

## 🔍 项目认证机制说明

### 🎉 当前版本：多用户模式（默认）

项目现在默认使用**多用户模式**，这意味着：

✅ **无需设置 AUTH_SECRET** - 简化了部署流程
✅ **支持自定义 TOKEN** - 每个用户可以设置自己的标识符
✅ **完全的数据隔离** - 不同用户的数据完全分离
✅ **更好的安全性** - 用户控制自己的访问凭证

### TOKEN（用户个人标识符）
- **作用**: 用户个人的Cookie数据标识符和访问凭证
- **设置位置**: 浏览器扩展设置中
- **设置方法**: 用户在扩展弹窗中自定义输入（支持自动生成）
- **使用场景**: 区分不同用户的Cookie数据，同时作为访问认证
- **设置次数**: 每个用户设置自己的TOKEN

### 🔄 从旧版本迁移

如果您之前使用的是需要 AUTH_SECRET 的版本：
1. 新版本完全向后兼容
2. 无需重新配置 Cloudflare Worker
3. 只需在扩展中设置您的自定义 TOKEN
4. 旧的 AUTH_SECRET 设置会被忽略（但不会影响功能）

## 🎯 TOKEN设置机制

### TOKEN是用户自定义的

**重要**: TOKEN完全由用户自定义设置，不是系统生成的！

```
用户需要在扩展设置中自己输入一个唯一的TOKEN
例如：user_alice_2024, my_personal_token, company_dept_001
```

### TOKEN的作用原理

```mermaid
graph LR
    A[用户A: token=alice_123] --> B[Worker API]
    C[用户B: token=bob_456] --> B
    B --> D[KV存储]
    D --> E[BING_COOKIE_alice_123]
    D --> F[BING_COOKIE_bob_456]
```

1. 用户A使用TOKEN `alice_123`，Cookie存储在 `BING_COOKIE_alice_123`
2. 用户B使用TOKEN `bob_456`，Cookie存储在 `BING_COOKIE_bob_456`
3. 两个用户的数据完全隔离，互不干扰

## 📝 TOKEN格式要求

### 基本要求
- **最小长度**: 6个字符
- **字符限制**: 只能包含字母、数字、下划线(_)、连字符(-)
- **大小写**: 区分大小写
- **唯一性**: 建议每个用户使用唯一的TOKEN

### 格式验证规则
```javascript
// TOKEN必须符合以下正则表达式
const tokenPattern = /^[a-zA-Z0-9_-]{6,}$/;

// 有效的TOKEN示例
✅ user_alice_2024
✅ my-personal-token
✅ company_IT_001
✅ project_webapp_prod

// 无效的TOKEN示例
❌ abc (太短)
❌ <EMAIL> (包含特殊字符)
❌ 用户名 (包含中文)
❌ user token (包含空格)
```

## 🎨 TOKEN命名建议

### 个人用户
```
格式: user_{姓名}_{年份/随机数}
示例:
- user_alice_2024
- user_bob_001
- user_john_dev
```

### 企业用户
```
格式: company_{公司名}_{部门}_{编号}
示例:
- company_acme_it_001
- company_microsoft_hr_002
- company_google_dev_team1
```

### 项目用户
```
格式: project_{项目名}_{环境}_{编号}
示例:
- project_webapp_prod_001
- project_api_test_002
- project_mobile_staging_001
```

### 家庭用户
```
格式: family_{姓氏}_{成员}_{设备}
示例:
- family_smith_dad_laptop
- family_jones_mom_desktop
- family_brown_son_tablet
```

## 🔐 TOKEN安全建议

### 安全级别

**低安全要求**（个人使用）:
```
user_alice_2024
my_bing_cookies
personal_token_001
```

**中等安全要求**（团队使用）:
```
team_project_alpha_001
dept_marketing_cookies_2024
group_research_bing_data
```

**高安全要求**（企业使用）:
```
corp_acme_secure_001_2024
enterprise_dept_it_prod_001
business_unit_sales_cookies_v2
```

### 安全最佳实践

1. **避免包含敏感信息**
   ```
   ❌ 不要使用: user_alice_password123
   ❌ 不要使用: company_secret_project
   ✅ 推荐使用: user_alice_cookies_2024
   ```

2. **定期更换TOKEN**
   ```
   建议每6-12个月更换一次TOKEN
   更换时需要重新配置扩展设置
   ```

3. **记录TOKEN信息**
   ```
   建议在安全的地方记录您的TOKEN
   避免遗忘导致无法访问历史Cookie数据
   ```

4. **团队TOKEN管理**
   ```
   企业用户建议建立TOKEN分配和管理制度
   避免TOKEN冲突和数据混乱
   ```

## 🛠️ TOKEN配置步骤

### 步骤1: 选择TOKEN
```
根据您的使用场景选择合适的TOKEN格式
确保TOKEN符合格式要求
检查TOKEN的唯一性
```

### 步骤2: 在扩展中设置
```
1. 点击浏览器中的扩展图标
2. 在"API Key (Secret)"字段中输入您的TOKEN
3. 点击"保存设置"
4. 确认看到"已保存!"提示
```

### 步骤3: 验证设置
```
1. 重新打开扩展弹窗
2. 确认TOKEN仍然显示在设置中
3. 点击"立即同步"测试功能
4. 检查扩展图标状态
```

## 🔄 TOKEN更换流程

### 何时需要更换TOKEN
- 安全考虑（定期更换）
- TOKEN泄露或被他人知晓
- 团队成员变动
- 项目环境变更

### 更换步骤
1. **备份当前数据**（可选）
   ```bash
   # 使用旧TOKEN获取当前Cookie数据
   curl "https://your-worker.workers.dev?token=old_token" > backup.txt
   ```

2. **设置新TOKEN**
   - 在扩展设置中输入新TOKEN
   - 保存设置

3. **迁移数据**（可选）
   ```bash
   # 使用新TOKEN上传备份的数据
   curl -X POST "https://your-worker.workers.dev?token=new_token" -d @backup.txt
   ```

4. **验证新TOKEN**
   - 执行同步测试
   - 确认数据正常

## ❓ 常见问题

### Q: TOKEN忘记了怎么办？
A: TOKEN无法找回，但可以设置新的TOKEN。旧TOKEN对应的数据仍然存在，如果想要访问需要使用原来的TOKEN。

### Q: 多个设备可以使用同一个TOKEN吗？
A: 可以。同一个TOKEN可以在多个设备上使用，它们会共享同一份Cookie数据。

### Q: TOKEN可以包含中文吗？
A: 不可以。TOKEN只能包含英文字母、数字、下划线和连字符。

### Q: TOKEN有长度限制吗？
A: 最少6个字符，建议不超过50个字符以保持简洁。

### Q: 如何确保TOKEN的唯一性？
A: 建议在TOKEN中包含个人标识、时间戳或随机数，例如：`user_alice_20240709_001`

## 📊 TOKEN使用示例

### 示例1: 个人用户
```
用户: Alice
TOKEN: user_alice_2024
用途: 个人Bing积分管理
```

### 示例2: 企业团队
```
公司: ACME Corp
部门: IT部门
TOKEN: acme_it_cookies_2024
用途: 团队共享Bing搜索数据
```

### 示例3: 多环境项目
```
项目: WebApp
环境: 生产环境
TOKEN: webapp_prod_bing_001
用途: 生产环境Bing API调用
```

## 🎉 总结

- TOKEN是用户自定义的个人标识符
- 用于区分不同用户的Cookie数据
- 必须符合格式要求：6+字符，字母数字下划线连字符
- 建议使用有意义的命名规则
- 定期更换以提高安全性
- 妥善保管避免遗忘

正确使用TOKEN可以确保您的Cookie数据安全隔离，实现多用户环境下的数据管理！
