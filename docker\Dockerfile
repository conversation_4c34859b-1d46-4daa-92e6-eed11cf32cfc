# Bing Rewards 自动化脚本 Docker 镜像
# 维护者: alxxxxla
# 基于 Python 3.11 Alpine 镜像

FROM python:3.11-alpine

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    TZ=Asia/Shanghai

# 安装系统依赖
RUN apk add --no-cache \
    tzdata \
    curl \
    ca-certificates \
    && ln -sf /usr/share/zoneinfo/$TZ /etc/localtime \
    && echo $TZ > /etc/timezone

# 复制 requirements.txt 并安装 Python 依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用程序文件
COPY la_auto_10_dingding..py ./bing_rewards.py

# 创建非 root 用户
RUN addgroup -g 1000 appuser && \
    adduser -D -u 1000 -G appuser appuser

# 切换到非 root 用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30m --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('https://www.bing.com', timeout=5)" || exit 1

# 设置默认命令
CMD ["python", "bing_rewards.py"]

# 标签信息
LABEL maintainer="alxxxxla" \
      description="Bing Rewards 自动化脚本容器" \
      version="2.0" \
      org.opencontainers.image.source="https://github.com/alxxxxla/bing-rewards-automation"
