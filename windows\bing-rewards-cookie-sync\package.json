{"name": "bing-rewards-cookie-sync", "version": "2.0.0", "description": "自动同步Microsoft Edge浏览器中Bing Rewards Cookie数据到Cloudflare Worker的浏览器扩展", "main": "background.js", "scripts": {"build": "node scripts/build.js", "test": "echo \"测试功能已集成到扩展中，请使用扩展的调试功能\"", "lint": "eslint *.js scripts/*.js", "clean": "<PERSON><PERSON>f build dist", "dev": "echo \"请在Edge浏览器中加载扩展进行开发\"", "package": "npm run build", "docs": "echo \"文档位于 docs/ 目录\"", "deploy-worker": "bash deploy-worker.sh", "generate-icons": "node scripts/generate-icons.js"}, "keywords": ["browser-extension", "edge-extension", "bing-rewards", "cookie-sync", "cloudflare-worker", "manifest-v3", "chrome-extension"], "author": {"name": "alxxxxla", "email": "<EMAIL>", "url": "https://github.com/alxxxxla"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/alxxxxla/bing-rewards-cookie-sync.git"}, "bugs": {"url": "https://github.com/alxxxxla/bing-rewards-cookie-sync/issues"}, "homepage": "https://github.com/alxxxxla/bing-rewards-cookie-sync#readme", "devDependencies": {"archiver": "^5.3.2", "eslint": "^8.55.0", "rimraf": "^5.0.5", "sharp": "^0.33.5"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": ["last 2 Chrome versions", "last 2 Edge versions"], "manifest": {"version": 3, "permissions": ["cookies", "storage", "alarms", "activeTab"], "host_permissions": ["https://*.bing.com/*", "https://*.workers.dev/*"]}, "directories": {"doc": "docs"}, "files": ["manifest.json", "background.js", "popup.html", "scripts/", "styles/", "icons/", "docs/", "cloudflare-worker/", "README.md", "LICENSE"]}