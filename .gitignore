# Bing Rewards 自动化项目 .gitignore
# 维护者: alxxxxla

# ==========================================
# 敏感文件 - 绝对不能提交
# ==========================================



# 环境变量配置文件
docker/.env
.env

# Cookie 和认证信息
cookie.txt
cookies/
auth/

# ==========================================
# 日志和临时文件
# ==========================================

# 日志文件
logs/
*.log
docker/logs/

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 备份文件
backup/
*.backup
*.bak

# ==========================================
# Node.js 相关
# ==========================================

# 依赖包
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 包管理器锁定文件（可选择性忽略）
# package-lock.json
# yarn.lock

# ==========================================
# Python 相关
# ==========================================

# 字节码文件
__pycache__/
*.py[cod]
*$py.class

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
*.manifest
*.spec

# 虚拟环境
venv/
env/
ENV/

# ==========================================
# Docker 相关
# ==========================================

# Docker 构建缓存
.dockerignore

# Docker Compose 覆盖文件
docker-compose.override.yml

# ==========================================
# 编辑器和 IDE
# ==========================================

# Visual Studio Code
.vscode/
*.code-workspace

# JetBrains IDEs
.idea/
*.iml

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc

# ==========================================
# 操作系统相关
# ==========================================

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ==========================================
# 版本控制
# ==========================================

# Git
.git/
*.orig

# SVN
.svn/

# ==========================================
# 压缩文件
# ==========================================

*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# ==========================================
# 测试和覆盖率
# ==========================================

# 测试覆盖率
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# ==========================================
# 项目特定
# ==========================================

# 构建输出
build/
dist/

# 配置文件（除了示例）
config.json
settings.json

# 数据文件
data/
*.db
*.sqlite
*.sqlite3

# 证书文件
*.crt
*.key
*.p12
*.pfx

# ==========================================
# 开发工具
# ==========================================

# ESLint
.eslintcache

# Prettier
.prettierignore

# TypeScript
*.tsbuildinfo

# Webpack
.webpack/

# ==========================================
# 注释说明
# ==========================================

# 如果需要包含某些被忽略的文件，使用 !filename 语法
# 例如：!important-config.example.json

# 如果需要忽略整个目录但保留其中某些文件：
# directory/
# !directory/keep-this-file.txt
