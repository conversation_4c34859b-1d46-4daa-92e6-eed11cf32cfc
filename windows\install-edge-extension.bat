@echo off
chcp 65001 >nul
cls

:: 设置控制台标题
title Bing Rewards Extension 安装助手

:: 扩展的唯一ID（已确认）
set "EXTENSION_ID=mplajoflljljogjfpilcocljdgjbgkla"

echo ===============================================
echo    Bing Rewards Extension 安装助手
echo    维护者: alxxxxla
echo ===============================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 检测到管理员权限
) else (
    echo [!] 警告：未检测到管理员权限
    echo     某些注册表操作可能需要管理员权限
    echo     建议右键选择"以管理员身份运行"
    echo.
    pause
)

echo [1/6] 正在配置注册表路径...
:: 创建必要的注册表项
reg add "HKCU\Software\Policies\Microsoft\Edge\ExtensionInstallAllowlist" /f 2>nul
reg add "HKCU\Software\Policies\Microsoft\Edge" /f 2>nul
reg add "HKCU\Software\Microsoft\Edge\Extensions" /f 2>nul

echo [2/6] 启用扩展安装权限...
:: 允许安装扩展
reg add "HKCU\Software\Policies\Microsoft\Edge" /v "AllowInstallingExtensions" /t REG_DWORD /d 1 /f >nul

echo [3/6] 将扩展加入白名单...
:: 将指定扩展ID加入白名单
reg add "HKCU\Software\Policies\Microsoft\Edge\ExtensionInstallAllowlist" /v "1" /t REG_SZ /d "%EXTENSION_ID%" /f >nul

echo [4/6] 启用开发者模式...
:: 启用开发者模式
reg add "HKCU\Software\Microsoft\Edge\Extensions" /v "DeveloperMode" /t REG_DWORD /d 1 /f >nul

echo [5/6] 清理可能的限制设置...
:: 清除可能的黑名单设置
reg delete "HKCU\Software\Policies\Microsoft\Edge\ExtensionInstallBlocklist" /f 2>nul >nul

:: 清除可能的强制安装限制
reg delete "HKCU\Software\Policies\Microsoft\Edge\ExtensionInstallForcelist" /f 2>nul >nul

echo [6/6] 配置完成！
echo.

echo ===============================================
echo    配置完成！现在可以安装扩展了
echo ===============================================
echo.
echo 📋 安装步骤：
echo    1. 打开 Microsoft Edge 浏览器
echo    2. 访问 edge://extensions/
echo    3. 确保右上角"开发人员模式"已开启
echo    4. 选择以下任一方式安装：
echo.
echo 🔧 方式一：拖拽安装（推荐）
echo    - 将 bing-rewards-cookie-sync.crx 文件
echo    - 直接拖拽到扩展页面
echo.
echo 🔧 方式二：解压安装
echo    - 点击"加载解压缩的扩展"
echo    - 选择 bing-rewards-cookie-sync 文件夹
echo.
echo 🔧 方式三：开发者加载
echo    - 点击"加载解压缩的扩展"
echo    - 浏览并选择扩展文件夹
echo.
echo ⚠️  注意事项：
echo    - 如果安装失败，请重启 Edge 浏览器后重试
echo    - 某些企业版 Edge 可能有额外限制
echo    - 安装成功后扩展图标会出现在工具栏
echo.
echo 🔗 相关链接：
echo    - 扩展页面: edge://extensions/
echo    - 开发者工具: edge://extensions/?developer=true
echo.

:: 询问是否立即打开扩展页面
echo 是否立即打开 Edge 扩展页面？(Y/N)
set /p choice=请选择: 
if /i "%choice%"=="Y" (
    echo 正在打开 Edge 扩展页面...
    start msedge.exe edge://extensions/
) else (
    echo 您可以稍后手动打开 edge://extensions/ 进行安装
)

echo.
echo ===============================================
echo    脚本执行完毕
echo    如有问题请联系维护者: alxxxxla
echo ===============================================
pause
