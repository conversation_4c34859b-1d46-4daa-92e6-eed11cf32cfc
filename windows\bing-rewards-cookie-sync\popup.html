<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Bing Rewards Cookie Sync</title>
    <link rel="stylesheet" href="styles/popup.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="icons/icon48.png" alt="icon" class="logo">
            <div class="title-section">
                <h1>Bing Rewards Sync</h1>
                <span id="sync-status-badge" class="badge">未知</span>
            </div>
        </div>

        <div class="card">
            <h2>同步状态</h2>
            <div class="status-grid">
                <span class="status-label">最后尝试:</span><span id="last-sync-time" class="status-value">从未</span>
                <span class="status-label success-label">最后成功:</span><span id="last-success-time" class="status-value success-value">从未成功</span>
                <span class="status-label">同步结果:</span><span id="last-sync-status" class="status-value">N/A</span>
            </div>
            <div id="error-details" class="error-message hidden">
                <strong>错误信息:</strong>
                <p id="last-error-message"></p>
            </div>
        </div>

        <div class="card">
            <h2>操作</h2>
            <button id="sync-now-btn" class="button button-primary">
                <span class="button-text">立即同步</span>
                <div class="spinner hidden"></div>
            </button>
            <button id="debug-alarm-btn" class="button button-secondary" style="margin-top: 8px;">
                <span class="button-text">🔍 检查定时器状态</span>
            </button>
        </div>

        <div class="card">
            <h2>设置</h2>
            <div class="settings-form">
                <div class="setting-item">
                    <label for="api-url" class="setting-label">Worker URL:</label>
                    <input type="text" id="api-url" class="input-field" placeholder="https://your-worker-name.your-account.workers.dev" value="https://bing-cookie-sync-worker.5s6.com" required>
                </div>
                
                <div class="setting-item api-key-container">
                    <label for="api-key" class="setting-label">API Key (TOKEN):</label>
                    <input type="text" id="api-key" placeholder="您的认证密钥" readonly>
                    <div class="api-key-controls">
                        <button type="button" id="generate-api-key-btn" class="button button-secondary" title="生成32位安全随机API Key">
                            <span class="button-text">🎲 自动生成</span>
                        </button>
                        <button type="button" id="edit-api-key-btn" class="button button-secondary" title="手动编辑API Key">
                            <span class="button-text">✏️ 手动编辑</span>
                        </button>
                        <button type="button" id="copy-api-key-btn" class="button button-secondary" title="复制API Key到剪贴板">
                            <span class="button-text">📋 复制</span>
                        </button>
                    </div>
                </div>
                
                <label for="sync-interval">同步频率 (分钟):</label>
                <input type="number" id="sync-interval" min="1" max="1440" value="240" placeholder="1-1440分钟 (1分钟到24小时)">

                <button id="save-settings-btn" class="button">保存设置</button>
                <div id="settings-saved-msg" class="save-status hidden">已保存!</div>
            </div>
        </div>
    </div>
    <script src="scripts/popup.js"></script>
    <script src="scripts/settings-diagnostic.js"></script>
    <script src="scripts/quick-setup.js"></script>
</body>
</html>
