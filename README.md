# Bing Rewards 自动化工具套件

> 🎯 **专业的 Bing Rewards 积分自动化解决方案**
> 维护者: **alxxxxla**
> 版本: **2.0**

## 📋 项目概述

这是一个完整的 Bing Rewards 积分自动化工具套件，包含：

- 🔧 **浏览器扩展**: 自动同步 Bing Cookie 到云端（5分钟间隔）
- 🤖 **Python 脚本**: 智能执行搜索任务和奖励任务
- 🐳 **Docker 支持**: 容器化部署，便于管理
- 📱 **钉钉通知**: 实时状态推送
- 🛠️ **安装工具**: Windows 环境一键配置

## 🏗️ 项目结构

```
bing-rewards-automation/
├── 📁 windows/                      # Windows 环境文件
│   ├── 📁 bing-rewards-cookie-sync/ # 浏览器扩展源码
│   ├── 📦 bing-rewards-cookie-sync.crx  # 扩展安装包
│   ├── 🔑 bing-rewards-cookie-sync.pem  # 扩展私钥
│   ├── 🔧 install-edge-extension.bat    # 批处理安装脚本
│   └── 🔧 Install-BingRewardsExtension.ps1  # PowerShell 安装脚本
├── 📁 docker/                       # Docker 环境文件
│   ├── 🐳 Dockerfile                # Docker 镜像配置
│   ├── 📋 docker-compose.yml        # Docker Compose 配置
│   ├── 🐍 la_auto_10_dingding..py   # Python 主脚本
│   ├── 📦 requirements.txt          # Python 依赖包
│   └── ⚙️ .env.example              # 环境变量模板
└── 📖 README.md                     # 项目主文档
```

## 🚀 快速开始

### 方式一：Windows 环境部署

#### 1. 安装浏览器扩展

**方法一：批处理脚本（推荐）**
```cmd
# 进入 windows 目录
cd windows\

# 右键以管理员身份运行
install-edge-extension.bat
```

**方法二：PowerShell 脚本**
```powershell
# 以管理员身份运行 PowerShell
cd windows\
.\Install-BingRewardsExtension.ps1

# 静默安装
.\Install-BingRewardsExtension.ps1 -Force -Quiet
```

**方法三：手动安装**
1. 打开 Microsoft Edge
2. 访问 `edge://extensions/`
3. 启用"开发人员模式"
4. 拖拽 `bing-rewards-cookie-sync.crx` 到页面
5. 确认安装

#### 2. 配置扩展
1. 安装完成后，点击扩展图标
2. 配置以下参数：
   - **API URL**: 您的 Cloudflare Worker 地址
   - **API Key**: 访问令牌
   - **同步间隔**: 默认5分钟（已优化）

#### 3. 验证安装
1. 登录 [Bing Rewards](https://rewards.bing.com/)
2. 检查扩展是否正常同步 Cookie
3. 查看扩展图标状态：
   - 🟢 **OK**: 同步成功
   - 🔴 **ERR**: 同步失败
   - 🟡 **CFG**: 需要配置

### 方式二：Docker 部署（推荐用于服务器）

#### 1. 环境准备
确保已安装：
- Docker Engine
- Docker Compose

#### 2. 配置环境变量
```bash
# 进入 docker 目录
cd docker/

# 复制配置模板
cp .env.example .env

# 编辑配置文件
nano .env  # 或使用其他编辑器
```

#### 3. 启动服务
```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 查看状态
docker-compose ps
```

## ⚙️ 配置说明

### 环境变量配置（Docker）

编辑 `docker/.env` 文件，配置以下参数：

#### 必需配置
```bash
# 钉钉通知配置
DINGDING_WEBHOOK=https://oapi.dingtalk.com/robot/send?access_token=your_token
DINGDING_SECRET=your_secret

# Cookie 获取 URL
COOKIE_URL=https://bing-cookie-sync-worker.5s6.com/?token=your_token
```

#### 可选配置
```bash
# 搜索任务配置
PC_MAX_PER_HOUR=4          # PC端每小时最大搜索次数
MOBILE_MAX_PER_HOUR=3      # 移动端每小时最大搜索次数
PC_MAX_DAILY=90            # PC端每日最大搜索次数
MOBILE_MAX_DAILY=60        # 移动端每日最大搜索次数

# 工作时间配置
WORKING_HOURS_START=6      # 开始工作时间
WORKING_HOURS_END=21       # 结束工作时间

# 奖励任务执行时间
REWARD_EXECUTION_TIMES=10,13,20  # 执行时间点（逗号分隔）

# 运行模式
RUN_MODE=single            # single: 单次运行, schedule: 定时运行
SCHEDULE_INTERVAL=60       # 定时运行间隔（分钟）
```

### 扩展 ID 管理

当前扩展 ID: `mplajoflljljogjfpilcocljdgjbgkla`

#### 重新打包扩展

**保持 ID 不变**（推荐）:
1. 使用现有的 `bing-rewards-cookie-sync.pem` 私钥
2. 在 `chrome://extensions/` 打包
3. 指定私钥文件路径

**生成新 ID**:
1. 删除或重命名 `.pem` 文件
2. 打包时不指定私钥文件
3. 更新安装脚本中的 ID

## ⚙️ 配置说明

### 环境变量配置

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DINGDING_WEBHOOK` | 钉钉机器人 Webhook URL | - |
| `DINGDING_SECRET` | 钉钉机器人密钥 | - |
| `COOKIE_URL` | Cookie 同步 URL | - |
| `PC_MAX_PER_HOUR` | PC端每小时最大搜索次数 | 4 |
| `MOBILE_MAX_PER_HOUR` | 移动端每小时最大搜索次数 | 3 |
| `PC_MAX_DAILY` | PC端每日最大搜索次数 | 90 |
| `MOBILE_MAX_DAILY` | 移动端每日最大搜索次数 | 60 |
| `WORKING_HOURS_START` | 开始工作时间 | 6 |
| `WORKING_HOURS_END` | 结束工作时间 | 21 |
| `REWARD_EXECUTION_TIMES` | 奖励任务执行时间 | 10,13,20 |

### 钉钉通知配置

1. 创建钉钉群机器人
2. 获取 Webhook URL 和密钥
3. 配置到环境变量中

## 🐳 Docker 使用指南

### 基础操作

```bash
# 进入 docker 目录
cd docker/

# 配置环境变量
cp .env.example .env
nano .env

# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 常用命令

```bash
# 查看容器状态
docker-compose ps

# 重启服务
docker-compose restart

# 进入容器调试
docker-compose exec bing-rewards /bin/sh

# 查看资源使用
docker stats

# 清理资源
docker-compose down --rmi all --volumes
```

### 运行模式

```bash
# 单次运行（默认）
docker-compose up

# 定时任务模式
docker-compose --profile scheduler up -d

# 自定义配置运行
docker-compose run --rm -e RUN_MODE=single bing-rewards
```

### 监控和维护

#### 健康检查
容器内置健康检查，每30分钟检查一次：

```bash
# 查看健康状态
docker-compose ps

# 手动执行健康检查
docker-compose exec bing-rewards python -c "import requests; requests.get('https://www.bing.com', timeout=5)"
```

#### 日志管理
日志配置已设置自动轮转：
- 最大文件大小：10MB
- 保留文件数：3个

#### 资源限制
默认资源限制：
- CPU：0.5核心
- 内存：256MB

## � 故障排除

### Windows 扩展问题

**Q: 扩展安装失败？**
A:
1. 确保以管理员身份运行安装脚本
2. 检查 Edge 版本是否支持
3. 尝试手动安装方式
4. 查看 Windows 事件日志

**Q: Cookie 同步失败？**
A:
1. 检查网络连接
2. 验证 API URL 和 Key
3. 确认已登录 Bing 账号
4. 查看扩展控制台日志

**Q: 权限问题？**
A:
1. 以管理员身份运行脚本
2. 检查 Windows 防火墙设置
3. 确认企业策略允许扩展安装

### Docker 部署问题

**Q: 容器启动失败？**
A:
1. **检查配置文件**:
   ```bash
   # 验证 .env 文件
   cat .env

   # 检查 docker-compose.yml 语法
   docker-compose config
   ```

2. **查看详细错误**:
   ```bash
   # 查看构建日志
   docker-compose build --no-cache

   # 查看启动日志
   docker-compose up
   ```

**Q: Cookie 获取失败？**
A:
1. **检查网络连接**:
   ```bash
   # 测试网络连通性
   docker-compose exec bing-rewards ping bing.com

   # 测试 Cookie URL
   docker-compose exec bing-rewards curl -I $COOKIE_URL
   ```

2. **验证配置**:
   ```bash
   # 检查环境变量
   docker-compose exec bing-rewards env | grep COOKIE_URL
   ```

**Q: 钉钉通知失败？**
A:
1. **验证 Webhook**:
   ```bash
   # 测试钉钉 Webhook
   curl -X POST $DINGDING_WEBHOOK \
     -H 'Content-Type: application/json' \
     -d '{"msgtype": "text", "text": {"content": "测试消息"}}'
   ```

2. **检查密钥配置**:
   ```bash
   # 验证密钥设置
   docker-compose exec bing-rewards env | grep DINGDING
   ```

## 📊 监控和日志

### 日志位置
- Docker: `./logs/` 目录
- 直接运行: 控制台输出

### 监控指标
- ✅ Cookie 有效性
- 📈 搜索进度
- 🎯 积分增长
- ⚠️ 错误统计
- 📱 通知状态

## 🔒 安全注意事项

1. **Cookie 安全**:
   - 定期更新 Cookie
   - 使用 HTTPS 传输
   - 避免在公共网络使用

2. **访问频率**:
   - 遵循平台限制
   - 使用智能延迟
   - 避免异常行为

3. **账号安全**:
   - 不要分享 Cookie
   - 定期检查账号状态
   - 及时处理异常通知

### 日志分析

关键日志标识：
- `[✓]`: 成功操作
- `[!]`: 警告信息
- `[✗]`: 错误信息
- `[📊]`: 进度信息
- `[⏰]`: 时间相关

##  更新日志

### v2.0 (当前版本)
- ✨ 插件默认同步间隔优化为5分钟
- 🎯 简化Cookie获取方式，专注URL模式
- 🐳 完整Docker化支持
- 📱 增强钉钉通知功能
- 🛠️ 改进Windows安装脚本
- 📊 优化日志输出和监控
- 🗂️ 重新组织项目结构，按平台分类
- 📖 精简项目文档

## 📞 联系方式

- 维护者: **alxxxxla**
- 问题反馈: 请通过 GitHub Issues

---

> ⚠️ **免责声明**: 本工具仅供学习研究使用，使用者需自行承担风险，遵守相关平台条款。

> 💡 **提示**: 如果您觉得这个项目有用，请给个 ⭐ Star 支持一下！


