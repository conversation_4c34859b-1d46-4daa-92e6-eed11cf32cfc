# 故障排除指南

本文档提供常见问题的诊断和解决方案。

## 🔍 问题诊断流程

### 1. 确定问题范围

首先确定问题发生在哪个组件：

```
用户操作 → 浏览器扩展 → API服务器 → MySQL数据库
```

### 2. 收集诊断信息

- 扩展版本和浏览器版本
- 错误消息和错误代码
- 操作系统和网络环境
- 最近的配置更改

### 3. 检查系统状态

```bash
# 检查API服务器状态
curl http://*************:3000/health

# 检查数据库连接
mysql -h ************* -u root -p -e "SELECT 1"

# 检查网络连通性
ping *************
telnet ************* 3000
```

## 🚨 常见问题及解决方案

### 扩展相关问题

#### 问题1: 扩展无法加载

**症状**:
- 扩展图标不显示
- 控制台显示加载错误
- 扩展列表中显示错误状态

**可能原因**:
- manifest.json格式错误
- 文件路径不正确
- 权限配置问题

**解决步骤**:

1. **检查manifest.json**:
```bash
# 验证JSON格式
cat manifest.json | python -m json.tool
```

2. **检查文件完整性**:
```bash
# 确保所有必需文件存在
ls -la background.js popup.html styles/ modules/ icons/
```

3. **重新安装扩展**:
   - 在 `edge://extensions/` 中移除扩展
   - 重新加载解压缩的扩展
   - 检查错误信息

#### 问题2: Cookie获取失败

**症状**:
- 同步状态显示"未找到Cookie"
- 日志显示Cookie数量为0
- 错误消息: "无法访问Cookie"

**诊断步骤**:

1. **检查登录状态**:
   - 访问 https://rewards.bing.com/
   - 确认已登录Microsoft账户
   - 检查Cookie是否存在

2. **验证扩展权限**:
```javascript
// 在扩展控制台中执行
chrome.cookies.getAll({domain: '.bing.com'}, (cookies) => {
  console.log('找到Cookie数量:', cookies.length);
  console.log('Cookie详情:', cookies);
});
```

3. **检查域名配置**:
```javascript
// 检查目标域名列表
console.log('目标域名:', cookieManager.targetDomains);
```

**解决方案**:
- 重新登录Bing Rewards
- 检查扩展权限设置
- 清除浏览器缓存后重试
- 重新安装扩展

#### 问题3: 同步状态异常

**症状**:
- 状态一直显示"同步中"
- 同步完成但状态未更新
- 错误状态无法清除

**解决步骤**:

1. **重置扩展状态**:
```javascript
// 在扩展控制台中执行
chrome.storage.local.clear(() => {
  console.log('存储已清除');
  location.reload();
});
```

2. **检查后台脚本**:
   - 打开 `edge://extensions/`
   - 点击扩展详情
   - 查看"背景页"日志

3. **手动触发同步**:
   - 点击"立即同步"按钮
   - 观察状态变化
   - 检查错误信息

### API服务器问题

#### 问题4: API服务器无法启动

**症状**:
```
Error: listen EADDRINUSE :::3000
Error: connect ECONNREFUSED 127.0.0.1:3306
```

**解决步骤**:

1. **检查端口占用**:
```bash
# 查找占用3000端口的进程
netstat -tulpn | grep :3000
lsof -i :3000

# 终止占用进程
kill -9 <PID>
```

2. **检查数据库连接**:
```bash
# 测试数据库连接
mysql -h ************* -u root -p -e "SELECT VERSION()"

# 检查数据库服务状态
systemctl status mysql
# 或
service mysql status
```

3. **检查配置文件**:
```bash
# 验证.env文件
cat .env | grep -v '^#' | grep -v '^$'

# 检查必需的环境变量
node -e "
require('dotenv').config();
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_USER:', process.env.DB_USER);
console.log('API_KEY:', process.env.API_KEY);
"
```

#### 问题5: API请求失败

**症状**:
```
HTTP 401 Unauthorized
HTTP 500 Internal Server Error
Connection timeout
```

**诊断步骤**:

1. **检查API密钥**:
```bash
# 测试健康检查端点
curl -H "X-API-Key: your-api-key" http://*************:3000/health

# 检查密钥配置
grep API_KEY .env
```

2. **查看服务器日志**:
```bash
# PM2日志
pm2 logs bing-rewards-api

# systemd日志
journalctl -u bing-rewards-api -f

# 直接运行查看输出
npm start
```

3. **测试数据库查询**:
```sql
-- 检查数据库连接
SELECT 1;

-- 检查表结构
SHOW TABLES;
DESCRIBE cookies;

-- 检查权限
SHOW GRANTS FOR CURRENT_USER();
```

### 数据库问题

#### 问题6: 数据库连接失败

**症状**:
```
ER_ACCESS_DENIED_ERROR: Access denied for user
ER_BAD_DB_ERROR: Unknown database
ECONNREFUSED: Connection refused
```

**解决步骤**:

1. **检查MySQL服务**:
```bash
# 检查服务状态
systemctl status mysql
ps aux | grep mysql

# 启动MySQL服务
systemctl start mysql
```

2. **验证用户权限**:
```sql
-- 检查用户是否存在
SELECT User, Host FROM mysql.user WHERE User = 'root';

-- 检查权限
SHOW GRANTS FOR 'root'@'%';

-- 创建用户（如果需要）
CREATE USER 'bing_sync'@'%' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON bing_db.* TO 'bing_sync'@'%';
FLUSH PRIVILEGES;
```

3. **检查网络配置**:
```bash
# 检查MySQL绑定地址
grep bind-address /etc/mysql/mysql.conf.d/mysqld.cnf

# 检查防火墙
ufw status
iptables -L

# 测试端口连通性
telnet ************* 3306
```

#### 问题7: 数据同步错误

**症状**:
```
ER_DUP_ENTRY: Duplicate entry
ER_DATA_TOO_LONG: Data too long for column
ER_NO_SUCH_TABLE: Table doesn't exist
```

**解决步骤**:

1. **检查表结构**:
```sql
-- 验证表是否存在
SHOW TABLES LIKE 'cookies';

-- 检查表结构
DESCRIBE cookies;

-- 重新创建表（如果需要）
SOURCE database/create_tables.sql;
```

2. **检查数据格式**:
```sql
-- 查看最近的错误记录
SELECT * FROM system_logs WHERE log_level = 'ERROR' ORDER BY created_at DESC LIMIT 10;

-- 检查重复数据
SELECT name, domain, path, COUNT(*) as count 
FROM cookies 
GROUP BY name, domain, path 
HAVING count > 1;
```

3. **清理问题数据**:
```sql
-- 删除重复记录（保留最新的）
DELETE c1 FROM cookies c1
INNER JOIN cookies c2 
WHERE c1.id < c2.id 
AND c1.name = c2.name 
AND c1.domain = c2.domain 
AND c1.path = c2.path;
```

## 🔧 高级诊断工具

### 扩展调试

1. **启用详细日志**:
```javascript
// 在background.js中设置
this.logger.setLogLevel('DEBUG');
```

2. **监控存储变化**:
```javascript
// 监听存储变化
chrome.storage.onChanged.addListener((changes, namespace) => {
  console.log('存储变化:', changes);
});
```

3. **手动测试模块**:
```javascript
// 测试Cookie管理器
const cookieManager = new CookieManager();
cookieManager.getBingRewardsCookies().then(cookies => {
  console.log('获取到的Cookie:', cookies);
});
```

### API服务器调试

1. **启用调试模式**:
```bash
# 设置环境变量
export DEBUG=*
export NODE_ENV=development

# 启动服务器
npm run dev
```

2. **数据库查询日志**:
```javascript
// 在server.js中添加
const pool = mysql.createPool({
  ...dbConfig,
  debug: true,
  trace: true
});
```

3. **请求响应日志**:
```javascript
// 添加中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} ${req.method} ${req.url}`);
  console.log('Headers:', req.headers);
  console.log('Body:', req.body);
  next();
});
```

### 数据库诊断

1. **性能分析**:
```sql
-- 启用慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 1;

-- 查看进程列表
SHOW PROCESSLIST;

-- 检查表状态
SHOW TABLE STATUS LIKE 'cookies';
```

2. **连接监控**:
```sql
-- 查看连接数
SHOW STATUS LIKE 'Connections';
SHOW STATUS LIKE 'Threads_connected';

-- 查看最大连接数
SHOW VARIABLES LIKE 'max_connections';
```

## 📊 监控和预防

### 设置监控

1. **API服务器监控**:
```bash
# 使用PM2监控
pm2 monit

# 设置健康检查
*/5 * * * * curl -f http://localhost:3000/health || echo "API服务器异常"
```

2. **数据库监控**:
```sql
-- 创建监控视图
CREATE VIEW v_system_health AS
SELECT 
  (SELECT COUNT(*) FROM cookies) as total_cookies,
  (SELECT COUNT(*) FROM sync_history WHERE DATE(start_time) = CURDATE()) as today_syncs,
  (SELECT COUNT(*) FROM system_logs WHERE log_level = 'ERROR' AND DATE(created_at) = CURDATE()) as today_errors;
```

3. **扩展监控**:
```javascript
// 定期健康检查
setInterval(async () => {
  try {
    const status = await this.getStatus();
    if (status.error) {
      console.warn('扩展状态异常:', status.error);
    }
  } catch (error) {
    console.error('健康检查失败:', error);
  }
}, 300000); // 5分钟
```

### 预防措施

1. **定期备份**:
```bash
# 数据库备份
mysqldump -h ************* -u root -p bing_db > backup_$(date +%Y%m%d).sql

# 配置文件备份
cp -r bing-rewards-cookie-sync backup/
```

2. **日志轮转**:
```bash
# 配置logrotate
cat > /etc/logrotate.d/bing-rewards-api << EOF
/var/log/bing-rewards-api/*.log {
    daily
    missingok
    rotate 30
    compress
    notifempty
    create 644 www-data www-data
}
EOF
```

3. **自动清理**:
```sql
-- 设置定期清理任务
CREATE EVENT cleanup_old_data
ON SCHEDULE EVERY 1 DAY
DO CALL CleanupExpiredData(30);
```

## 📞 获取帮助

如果以上解决方案都无法解决问题，请：

1. **收集诊断信息**:
   - 扩展版本和浏览器版本
   - 完整的错误消息和堆栈跟踪
   - 相关的日志文件
   - 系统环境信息

2. **提交Issue**:
   - 访问 GitHub Issues 页面
   - 使用问题模板
   - 提供详细的重现步骤

3. **联系维护者**:
   - GitHub: @alxxxxla
   - 邮箱: [维护者邮箱]

---

更多技术支持请参考[开发者文档](DEVELOPMENT.md)。
