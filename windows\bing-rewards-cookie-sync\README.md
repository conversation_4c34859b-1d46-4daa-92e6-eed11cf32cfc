# Bing Rewards Cookie Sync (Cloudflare Edition)

一个使用 Cloudflare Workers 和 KV 存储，将 Microsoft Edge 浏览器中的 Bing.com Cookie 同步到云端的轻量级浏览器扩展。

## 🚀 核心优势

- **零服务器成本**: 完全运行在 Cloudflare 的免费套餐上，无需自己的服务器或数据库。
- **部署简单**: 只需几个简单的命令行步骤即可将后端 API 部署到 Cloudflare 的全球网络。
- **高可用与安全**: 借助 Cloudflare 的基础设施，API 稳定可靠，并通过授权密钥保护。
- **维护轻松**: 一次部署，几乎无需后续维护。

## 🏛️ 系统架构

项目被设计为两个核心部分：

1.  **浏览器扩展**: 运行在您的浏览器中，负责从 `bing.com` 获取 Cookie。
2.  **Cloudflare Worker**: 部署在 Cloudflare 上的轻量级后端，负责接收并存储 Cookie 字符串到 KV 存储中。

数据流如下:
```mermaid
graph LR;
    A["浏览器插件 (Edge)"] -- "发送Cookie字符串" --> B["Cloudflare Worker API"];
    B -- "存储到KV" --> C["Cloudflare KV 命名空间"];
```

## 🛠️ 安装与部署

整个过程分为三步：准备 Cloudflare 环境、部署 Worker、安装和配置浏览器扩展。

> 📖 **完整配置指南**: 查看 [完整配置流程指南](docs/COMPLETE-SETUP-GUIDE.md) 获取详细的步骤说明和故障排除方案
> 📁 **项目结构**: 查看 [项目结构说明](PROJECT_STRUCTURE.md) 了解项目文件组织和维护指南

### 第1步: 准备 Cloudflare 环境

1.  **注册 Cloudflare 账户**: 如果您还没有，请前往 [Cloudflare 官网](https://www.cloudflare.com/) 注册一个免费账户。
2.  **安装 Wrangler CLI**: Wrangler 是 Cloudflare 的官方命令行工具，用于管理 Workers。请根据 [官方文档](https://developers.cloudflare.com/workers/wrangler/install-and-update/) 安装它。
    ```bash
    npm install -g wrangler
    ```
3.  **登录 Wrangler**:
    ```bash
    wrangler login
    ```
4.  **创建 KV 命名空间**: 我们需要一个 KV 命名空间来存储 Cookie。
    ```bash
    # 这会创建一个名为 BING_KV 的命名空间
    wrangler kv namespace create "BING_KV"
    ```
    执行后，记下输出的 `id`，我们稍后会用到。

### 第2步: 部署 Cloudflare Worker

1.  **进入 Worker 目录**:
    ```bash
    cd bing-rewards-cookie-sync/cloudflare-worker
    ```

2.  **检查并编辑 `wrangler.toml` 配置文件**:
    ```bash
    # 查看当前配置
    cat wrangler.toml
    ```

    确保配置文件包含正确的KV命名空间ID：
    ```toml
    # Cloudflare Worker配置 - 多用户支持版本（默认）
    name = "bing-cookie-sync-worker"
    main = "index-multiuser.js"  # 使用多用户版本
    compatibility_date = "2023-12-01"

    [[kv_namespaces]]
    binding = "BING_KV"
    id = "您的KV命名空间ID"  # 替换为您的KV ID
    ```

    **注意**：项目现在默认使用多用户模式，支持自定义TOKEN，无需设置AUTH_SECRET。

3.  **部署 Worker**:
    ```bash
    wrangler deploy
    ```

    部署成功后，您会看到类似输出：
    ```
    ✅ Successfully published your Worker to
    https://bing-cookie-sync-worker.your-account.workers.dev
    ```

    **请记录这个Worker URL**，稍后在扩展配置中需要使用。

5.  **验证部署**:
    ```bash
    # 测试Worker是否正常运行（使用任意6位以上的token）
    curl "https://your-worker-url.workers.dev?token=test123"
    ```

    应该返回：`Cookie not found for this user.`
    这表明Worker正常运行，多用户模式已启用。

### 第3步: 安装并配置浏览器扩展

1.  **构建扩展和生成图标**:
    ```bash
    # 安装依赖
    npm install
    
    # 生成图标文件（解决图标问题）
    npm run generate-icons
    
    # 构建扩展包
    npm run build
    ```

2.  **加载扩展**:
    - 打开 Microsoft Edge 浏览器，访问 `edge://extensions/`
    - 开启右上角的"开发人员模式"
    - 点击"加载解压缩的扩展"
    - 选择本项目的 `build` 目录或 `bing-rewards-cookie-sync` 根目录

2.  **配置扩展设置**:
    - 点击浏览器工具栏上新出现的扩展图标
    - 在弹出页面中，填入以下信息：

    **Worker URL**:
    ```
    https://your-worker-name.your-account.workers.dev
    ```
    例如：`https://bing-cookie-sync-worker.alice123.workers.dev`

    **API Key (TOKEN)**:
    ```
    your_personal_token
    ```
    例如：`user_alice_2024` 或 `my_secure_token_123`

    > 📖 **TOKEN详细说明**: 查看 [TOKEN使用指南](docs/TOKEN-GUIDE.md) 了解TOKEN格式要求、命名建议和安全最佳实践

    **同步频率**: 选择1-1440分钟之间的值（默认240分钟，即4小时）

3.  **保存并验证配置**:
    - 点击"保存设置"按钮
    - 确认看到"已保存!"提示信息
    - 设置会自动保存到浏览器本地存储中

4.  **重要说明**:
    - **Worker URL**: 您在第2步部署时获得的Cloudflare Worker地址
    - **API Key**: 这是您的个人TOKEN，用于标识您的Cookie数据
    - **TOKEN格式**: 建议使用字母、数字、下划线组合，至少6个字符
    - **数据隔离**: 不同的TOKEN对应不同的Cookie存储空间

## ✅ 使用方法和测试验证

### 基本使用

- **自动同步**: 扩展默认每240分钟（4小时）自动同步一次，可在设置中修改同步频率（1-1440分钟）
- **手动同步**: 点击扩展弹窗中的"立即同步"按钮，立即触发同步
- **状态查看**: 扩展图标显示同步状态：
  - `OK` - 同步成功
  - `ERR` - 同步失败
  - `CFG` - 配置错误，需要设置Worker URL和TOKEN
  - `NO` - 未找到Cookie，请确保已登录Bing.com

### 完整测试验证流程

#### 1. 前置准备
```bash
# 确保已登录Bing.com
# 访问 https://www.bing.com 并登录您的Microsoft账户
# 访问 https://rewards.bing.com 确认能看到积分信息
```

#### 2. 配置验证
- 打开扩展弹窗，确认设置已保存
- Worker URL格式正确：`https://xxx.workers.dev`
- TOKEN已设置且符合格式要求

#### 3. 手动同步测试
- 点击"立即同步"按钮
- 观察扩展图标状态变化
- 检查弹窗中的同步时间和状态信息

#### 4. Worker连接测试
```bash
# 在浏览器中直接访问（替换为您的实际URL和TOKEN）
https://your-worker.workers.dev?token=your_token

# 正常情况下应该返回：
# "Cookie not found" (首次同步前) 或 实际的Cookie字符串
```

#### 5. 完整流程验证
1. 执行手动同步，扩展图标应显示"OK"
2. 在浏览器中访问Worker URL，应返回Cookie数据
3. 等待自动同步间隔，验证自动同步功能
4. 重新加载扩展，确认设置仍然保存

## 🛠️ 常见问题与故障排除

### 图标无法显示问题

如果您遇到图标无法正常显示或导入扩展时提示"无法对图像进行解码：icon128.png"：

1. **解决方法**：执行图标生成脚本
   ```bash
   # 安装依赖
   npm install
   
   # 生成图标文件
   npm run generate-icons
   ```

2. **重新构建扩展**
   ```bash
   npm run build
   ```

3. **检查生成的图标文件**
   确保 `icons` 目录中已经生成了以下文件：
   - icon16.png
   - icon48.png
   - icon128.png
   - icon.svg

4. **如果问题仍然存在**
   - 确保安装了 `sharp` 库
   - 检查 SVG 文件是否正确
   - 尝试使用其他工具转换 SVG 到 PNG

## 🔍 查看和验证存储的 Cookie

### 📖 GET 请求查看 Cookie

您可以通过简单的 GET 请求查看当前存储在 Cloudflare KV 中的最新 Cookie 字符串：

#### 方法1: 浏览器直接访问
```
https://your-worker.workers.dev?token=your-secret-key
```
直接在浏览器地址栏输入上述URL（替换为您的实际Worker URL和密钥），即可查看存储的Cookie。

#### 方法2: 命令行工具
```bash
# 使用 curl
curl "https://your-worker.workers.dev?token=your-secret-key"

# 使用 wget
wget -qO- "https://your-worker.workers.dev?token=your-secret-key"

# 使用 PowerShell (Windows)
Invoke-RestMethod "https://your-worker.workers.dev?token=your-secret-key"
```

#### 方法3: 在线API测试工具
- 使用 [Postman](https://www.postman.com/)
- 使用 [Insomnia](https://insomnia.rest/)
- 使用 [HTTPie](https://httpie.io/app)

### 📋 响应说明

**成功响应**：
```
MUID=20C75A143E2B617F033A4EB13F51608C; SRCHD=AF=NOFORM; SRCHUID=V=2&GUID=A3DE55CAB0364260A58E759892F402D6...
```

**错误响应**：
- `Unauthorized: Missing or incorrect token parameter.` - 密钥错误或缺失
- `Cookie not found` - 还未同步过任何Cookie
- `Internal Server Error` - Worker内部错误

### 🔧 集成到其他脚本

您可以在其他自动化脚本中使用这个API获取最新的Cookie：

```python
# Python 示例
import requests

def get_bing_cookies():
    url = "https://your-worker.workers.dev"
    params = {"token": "your-secret-key"}
    
    response = requests.get(url, params=params)
    if response.status_code == 200:
        return response.text
    else:
        print(f"获取Cookie失败: {response.text}")
        return None

cookies = get_bing_cookies()
if cookies:
    print("获取到Cookie:", cookies[:100] + "...")
```

```javascript
// JavaScript 示例
async function getBingCookies() {
    const url = "https://your-worker.workers.dev?token=your-secret-key";
    
    try {
        const response = await fetch(url);
        if (response.ok) {
            return await response.text();
        } else {
            console.error("获取Cookie失败:", await response.text());
            return null;
        }
    } catch (error) {
        console.error("网络错误:", error);
        return null;
    }
}
```

## 🔧 故障排除

### 常见问题解决

#### "立即同步"按钮不起作用
1. **检查配置**: 确保已正确设置Worker URL和API密钥
2. **检查登录状态**: 确保已登录Bing.com并有相关Cookie
3. **重新加载扩展**: 在`edge://extensions/`中重新加载扩展
4. **检查权限**: 确保扩展有访问`*.workers.dev`的权限

#### 扩展图标状态说明
- **OK** - 同步成功
- **ERR** - 同步失败，查看弹窗中的详细错误信息
- **CFG** - 配置错误，需要设置Worker URL和API密钥
- **NO** - 未找到Cookie，请先登录Bing.com

#### 设置无法保存或重新加载后消失
**症状**: 点击"保存设置"后，重新打开扩展弹窗设置消失

**解决方案**:
1. **检查输入格式**:
   - Worker URL必须以`https://`开头
   - TOKEN至少6个字符，只能包含字母、数字、下划线、连字符
   - 同步频率在1-1440分钟之间（1分钟到24小时）

2. **使用诊断工具**:
   ```javascript
   // 右键扩展弹窗 → "检查" → 控制台中运行
   runFullDiagnostic()  // 诊断存储问题
   checkSetupStatus()   // 检查当前设置状态
   ```

3. **快速修复**:
   ```javascript
   // 在控制台中运行快速设置
   quickSetup()

   // 或者强制保存设置
   forceSaveSettings('https://your-worker-url.workers.dev', 'your_token')
   ```

4. **重置扩展**:
   ```javascript
   // 清除所有设置重新开始
   fixSettingsIssues()
   ```

5. **手动验证**:
   - 保存设置后不要立即关闭弹窗
   - 等待2-3秒再关闭
   - 重新打开确认设置是否保存

#### 网络连接问题
1. **测试Worker连接**: 在浏览器中直接访问`https://your-worker.workers.dev?token=your-key`
2. **检查防火墙**: 确保没有阻止访问Cloudflare Workers
3. **检查代理设置**: 如果使用代理，确保配置正确

### 功能说明

#### 手动同步 vs 自动同步
- **手动同步**: 点击"立即同步"按钮立即执行，不受自动同步设置影响
- **自动同步**: 根据设置的时间间隔（1-1440分钟）自动执行

#### Cookie获取范围
扩展会从以下域名获取Cookie：
- `bing.com`
- `.bing.com`
- `www.bing.com`
- `rewards.bing.com`

#### 权限要求
- `cookies` - 读取Bing相关Cookie
- `storage` - 保存扩展设置
- `alarms` - 定时自动同步
- `https://*.bing.com/` - 访问Bing域名
- `https://*.workers.dev/` - 访问Cloudflare Worker

## 📝 更新日志

### v2.0.0 (2025-07-09)
- ✅ 修复了"立即同步"按钮不响应的问题
- ✅ 修复了设置无法保存的问题
- ✅ 改进了Cookie获取逻辑，支持更多域名
- ✅ 优化了错误处理和用户反馈
- ✅ 添加了对Cloudflare Workers域名的访问权限
- ✅ 改进了网络连接错误的诊断信息
- ✅ 手动同步现在不受自动同步设置影响
- ✅ 更新了README文档，添加了详细的使用说明

### 主要修复
1. **模块导入错误** - 修复了background.js中的ES6模块导入问题
2. **权限配置** - 添加了`*.workers.dev`域名访问权限
3. **Cookie获取** - 改进了Cookie检测和过滤逻辑
4. **错误处理** - 提供了更详细的错误信息和解决建议

## 📄 许可证

本项目采用MIT许可证。有关详细信息，请参阅 [LICENSE](LICENSE) 文件。
