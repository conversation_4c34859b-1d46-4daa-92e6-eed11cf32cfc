// 快速设置工具 - 解决设置持久化问题

console.log('⚡ 快速设置工具已加载');

// 快速设置函数
async function quickSetup() {
    console.log('🚀 开始快速设置...');
    
    // 获取用户输入
    const workerUrl = prompt('请输入您的Worker URL:\n例如: https://bing-cookie-sync-worker.alice123.workers.dev');
    if (!workerUrl) {
        console.log('❌ 设置已取消');
        return;
    }
    
    const token = prompt('请输入您的TOKEN:\n例如: user_alice_2024');
    if (!token) {
        console.log('❌ 设置已取消');
        return;
    }
    
    const syncInterval = prompt('请输入同步频率（小时，1-24）:', '4');
    const interval = parseInt(syncInterval, 10);
    
    // 验证输入
    if (!workerUrl.startsWith('https://')) {
        alert('Worker URL必须以https://开头');
        return;
    }
    
    if (token.length < 6) {
        alert('TOKEN至少需要6个字符');
        return;
    }
    
    if (interval < 1 || interval > 24) {
        alert('同步频率必须在1-24小时之间');
        return;
    }
    
    // 保存设置
    const settings = {
        apiUrl: workerUrl,
        apiKey: token,
        syncInterval: interval,
        autoSync: true,
        lastUpdated: new Date().toISOString(),
        version: '2.0.0',
        quickSetup: true
    };
    
    try {
        await new Promise((resolve, reject) => {
            chrome.storage.local.set(settings, () => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve();
                }
            });
        });
        
        console.log('✅ 设置保存成功');
        alert('设置保存成功！\n\n请：\n1. 关闭此弹窗\n2. 重新打开扩展\n3. 确认设置已保存\n4. 点击"立即同步"测试');
        
        // 验证保存
        setTimeout(async () => {
            const saved = await new Promise((resolve) => {
                chrome.storage.local.get(['apiUrl', 'apiKey'], resolve);
            });
            
            if (saved.apiUrl === workerUrl && saved.apiKey === token) {
                console.log('✅ 设置验证成功');
            } else {
                console.warn('⚠️ 设置验证失败');
            }
        }, 1000);
        
    } catch (error) {
        console.error('❌ 设置保存失败:', error);
        alert('设置保存失败: ' + error.message);
    }
}

// 检查设置状态
async function checkSetupStatus() {
    console.log('🔍 检查设置状态...');
    
    try {
        const settings = await new Promise((resolve, reject) => {
            chrome.storage.local.get(['apiUrl', 'apiKey', 'syncInterval', 'hasShownInitialPrompt'], (result) => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve(result);
                }
            });
        });
        
        console.log('📋 当前设置:', settings);
        
        const status = {
            hasWorkerUrl: !!settings.apiUrl,
            hasToken: !!settings.apiKey,
            hasSyncInterval: !!settings.syncInterval,
            isComplete: !!(settings.apiUrl && settings.apiKey && settings.syncInterval)
        };
        
        console.log('📊 设置状态:', status);
        
        if (status.isComplete) {
            console.log('✅ 设置完整');

            // 只在首次配置完成时显示测试连接提示
            if (!settings.hasShownInitialPrompt) {
                console.log('🎉 首次配置完成，显示测试连接提示');

                if (confirm('设置已完整，是否测试Worker连接？')) {
                    await testWorkerConnection(settings.apiUrl, settings.apiKey);
                }

                // 标记已显示过首次提示
                await new Promise((resolve) => {
                    chrome.storage.local.set({ hasShownInitialPrompt: true }, () => {
                        console.log('✅ 已标记首次提示已显示');
                        resolve();
                    });
                });
            } else {
                console.log('📝 设置完整，跳过重复提示');
            }
        } else {
            console.log('⚠️ 设置不完整');
            
            const missing = [];
            if (!status.hasWorkerUrl) missing.push('Worker URL');
            if (!status.hasToken) missing.push('TOKEN');
            if (!status.hasSyncInterval) missing.push('同步频率');
            
            console.log('❌ 缺少:', missing.join(', '));
            
            if (confirm(`缺少设置: ${missing.join(', ')}\n\n是否运行快速设置？`)) {
                await quickSetup();
            }
        }
        
        return status;
        
    } catch (error) {
        console.error('❌ 检查设置失败:', error);
        return null;
    }
}

// 测试Worker连接
async function testWorkerConnection(workerUrl, token) {
    console.log('🌐 测试Worker连接...');
    
    try {
        const testUrl = `${workerUrl}?token=${token}`;
        const response = await fetch(testUrl);
        
        console.log('📡 响应状态:', response.status);
        
        if (response.ok) {
            const data = await response.text();
            console.log('✅ Worker连接成功');
            console.log('📄 响应数据:', data.substring(0, 100) + '...');
            alert('Worker连接测试成功！\n\n您可以开始使用同步功能了。');
        } else if (response.status === 404) {
            console.log('📭 Worker连接正常，但还没有Cookie数据');
            alert('Worker连接正常！\n\n还没有Cookie数据，请点击"立即同步"开始同步。');
        } else {
            const errorText = await response.text();
            console.error('❌ Worker响应错误:', errorText);
            alert('Worker连接失败:\n' + errorText);
        }
        
    } catch (error) {
        console.error('❌ Worker连接失败:', error);
        alert('Worker连接失败:\n' + error.message + '\n\n请检查:\n1. Worker URL是否正确\n2. 网络连接是否正常\n3. Worker是否正在运行');
    }
}

// 修复设置问题
async function fixSettingsIssues() {
    console.log('🔧 修复设置问题...');
    
    // 1. 清除可能损坏的数据
    console.log('1️⃣ 清除可能损坏的数据...');
    try {
        await new Promise((resolve, reject) => {
            chrome.storage.local.clear(() => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve();
                }
            });
        });
        console.log('✅ 清除完成');
    } catch (error) {
        console.error('❌ 清除失败:', error);
    }
    
    // 2. 重新设置
    console.log('2️⃣ 重新设置...');
    await quickSetup();
}

// 导出到全局
if (typeof window !== 'undefined') {
    window.quickSetup = quickSetup;
    window.checkSetupStatus = checkSetupStatus;
    window.testWorkerConnection = testWorkerConnection;
    window.fixSettingsIssues = fixSettingsIssues;
}

// 自动检查
if (typeof chrome !== 'undefined' && chrome.storage) {
    setTimeout(() => {
        console.log('💡 可用命令:');
        console.log('- quickSetup() - 快速设置');
        console.log('- checkSetupStatus() - 检查设置状态');
        console.log('- fixSettingsIssues() - 修复设置问题');
        
        // 自动检查设置状态
        checkSetupStatus();
    }, 2000);
}
