document.addEventListener('DOMContentLoaded', () => {
    // --- DOM Elements ---
    const syncStatusBadge = document.getElementById('sync-status-badge');
    const lastSyncTimeEl = document.getElementById('last-sync-time');
    const lastSuccessTimeEl = document.getElementById('last-success-time');
    const lastSyncStatusEl = document.getElementById('last-sync-status');
    const errorDetailsEl = document.getElementById('error-details');
    const lastErrorMessageEl = document.getElementById('last-error-message');

    const syncNowBtn = document.getElementById('sync-now-btn');
    const syncNowBtnText = syncNowBtn ? syncNowBtn.querySelector('.button-text') : null;
    const syncNowSpinner = syncNowBtn ? syncNowBtn.querySelector('.spinner') : null;

    const debugAlarmBtn = document.getElementById('debug-alarm-btn');

    const apiUrlInput = document.getElementById('api-url');
    const apiKeyInput = document.getElementById('api-key');
    const syncIntervalInput = document.getElementById('sync-interval');
    const saveSettingsBtn = document.getElementById('save-settings-btn');
    const settingsSavedMsg = document.getElementById('settings-saved-msg');

    // API Key 控制按钮
    const generateApiKeyBtn = document.getElementById('generate-api-key-btn');
    const editApiKeyBtn = document.getElementById('edit-api-key-btn');
    const copyApiKeyBtn = document.getElementById('copy-api-key-btn');

    // 调试信息：检查关键元素是否正确获取
    console.log('🔍 弹窗脚本已加载，DOM元素检查:');
    console.log('- syncNowBtn:', syncNowBtn ? '✅' : '❌');
    console.log('- generateApiKeyBtn:', generateApiKeyBtn ? '✅' : '❌');
    console.log('- saveSettingsBtn:', saveSettingsBtn ? '✅' : '❌');



    // --- Functions ---

    /**
     * 数据迁移函数：处理向后兼容性
     */
    const migrateData = async (data) => {
        let needsMigration = false;
        const updates = {};

        // 检查是否需要迁移 lastSuccessTime
        if (data.lastSyncTime && !data.lastSuccessTime && data.lastSyncStatus === 'success') {
            // 如果有 lastSyncTime 且最后状态是成功，但没有 lastSuccessTime
            updates.lastSuccessTime = data.lastSyncTime;
            needsMigration = true;
            console.log('🔄 迁移数据：设置 lastSuccessTime =', new Date(data.lastSyncTime).toLocaleString());
        }

        if (needsMigration) {
            await chrome.storage.local.set(updates);
            console.log('✅ 数据迁移完成');
        }

        return { ...data, ...updates };
    };

    /**
     * Updates the UI with the latest data from chrome.storage.
     */
    const updateUI = async (data) => {
        // 首先进行数据迁移
        data = await migrateData(data);
        // Settings - 增强的数据加载逻辑
        if (apiUrlInput) {
            const savedUrl = data.apiUrl || data.workerUrl || '';
            // 如果没有保存的URL，保持HTML中设置的默认值
            if (savedUrl) {
                apiUrlInput.value = savedUrl;
                console.log('加载已保存的Worker URL:', savedUrl);
            } else {
                console.log('使用默认Worker URL:', apiUrlInput.value);
            }
        }

        if (apiKeyInput) {
            const savedKey = data.apiKey || data.token || '';
            apiKeyInput.value = savedKey;
            console.log('加载API Key:', savedKey ? '已设置' : '未设置');
        }

        if (syncIntervalInput) {
            let savedInterval = data.syncInterval || 240; // 默认240分钟（4小时）

            // 向后兼容：检测是否为旧的小时值（通常 ≤ 24）
            if (savedInterval <= 24 && !data.intervalUnit) {
                // 可能是旧的小时值，转换为分钟
                savedInterval = savedInterval * 60;
                console.log('检测到旧的小时值，已转换为分钟:', savedInterval);

                // 保存转换后的值和单位标记
                chrome.storage.local.set({
                    syncInterval: savedInterval,
                    intervalUnit: 'minutes'
                });
            }

            syncIntervalInput.value = savedInterval;
            console.log('加载同步间隔:', savedInterval, '分钟');
        }

        // Sync Status - 分别处理两个时间字段
        // 最后尝试时间（包括成功和失败的尝试）
        if (data.lastSyncTime) {
            lastSyncTimeEl.textContent = new Date(data.lastSyncTime).toLocaleString();
        } else {
            lastSyncTimeEl.textContent = '从未';
        }

        // 最后成功时间（仅成功的同步）
        if (data.lastSuccessTime) {
            lastSuccessTimeEl.textContent = new Date(data.lastSuccessTime).toLocaleString();
            lastSuccessTimeEl.className = 'status-value success-value';
        } else {
            lastSuccessTimeEl.textContent = '从未成功';
            lastSuccessTimeEl.className = 'status-value success-value never-success';
        }

        const statusMap = {
            'success': { text: '成功', className: 'badge-success' },
            'error': { text: '失败', className: 'badge-error' },
            'no_cookies': { text: '无Cookie', className: 'badge-warn' },
            'none': { text: '未知', className: 'badge-neutral' },
            'syncing': { text: '同步中', className: 'badge-syncing' }
        };
        const statusInfo = statusMap[data.lastSyncStatus] || statusMap['none'];
        
        syncStatusBadge.textContent = statusInfo.text;
        syncStatusBadge.className = `badge ${statusInfo.className}`;
        lastSyncStatusEl.textContent = statusInfo.text;

        // Also update the button state based on the status
        setSyncButtonLoading(data.lastSyncStatus === 'syncing');

        // Error message
        if (data.lastSyncStatus === 'error' && data.lastError) {
            lastErrorMessageEl.textContent = data.lastError;
            errorDetailsEl.classList.remove('hidden');
        } else {
            errorDetailsEl.classList.add('hidden');
        }
    };

    /**
     * Toggles the sync button state between idle and loading.
     */
    const setSyncButtonLoading = (isLoading) => {
        if (isLoading) {
            syncNowBtn.disabled = true;
            syncNowBtnText.textContent = '同步中...';
            syncNowSpinner.classList.remove('hidden');
        } else {
            syncNowBtn.disabled = false;
            syncNowBtnText.textContent = '立即同步';
            syncNowSpinner.classList.add('hidden');
        }
    };

    /**
     * 生成32位安全随机API Key
     * 使用crypto.getRandomValues()生成符合TOKEN格式要求的密钥
     */
    const generateSecureApiKey = () => {
        // 定义允许的字符集：字母、数字、下划线、连字符
        const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_-';
        const keyLength = 32;

        // 使用crypto.getRandomValues()生成安全随机数
        const randomValues = new Uint8Array(keyLength);
        crypto.getRandomValues(randomValues);

        // 将随机数转换为字符
        let apiKey = '';
        for (let i = 0; i < keyLength; i++) {
            apiKey += charset[randomValues[i] % charset.length];
        }

        // 确保以字母开头（符合TOKEN最佳实践）
        if (!/^[A-Za-z]/.test(apiKey)) {
            const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
            const randomLetter = new Uint8Array(1);
            crypto.getRandomValues(randomLetter);
            apiKey = letters[randomLetter[0] % letters.length] + apiKey.slice(1);
        }

        return apiKey;
    };

    /**
     * 验证API Key格式
     */
    const validateApiKey = (key) => {
        if (!key || key.length < 6) {
            return { valid: false, message: 'API Key至少需要6个字符' };
        }

        if (!/^[a-zA-Z0-9_-]+$/.test(key)) {
            return { valid: false, message: 'API Key只能包含字母、数字、下划线和连字符' };
        }

        return { valid: true, message: '' };
    };

    /**
     * 设置API Key输入框的编辑状态
     */
    const setApiKeyEditMode = (isEditing) => {
        if (isEditing) {
            apiKeyInput.readOnly = false;
            apiKeyInput.focus();
            apiKeyInput.select();
            editApiKeyBtn.querySelector('.button-text').textContent = '✅ 完成';
        } else {
            apiKeyInput.readOnly = true;
            editApiKeyBtn.querySelector('.button-text').textContent = '✏️ 手动编辑';
        }
    };

    /**
     * 复制API Key到剪贴板
     */
    const copyApiKeyToClipboard = async () => {
        const apiKey = apiKeyInput.value.trim();
        if (!apiKey) {
            alert('没有API Key可复制');
            return;
        }

        try {
            await navigator.clipboard.writeText(apiKey);

            // 临时显示复制成功状态
            const originalText = copyApiKeyBtn.querySelector('.button-text').textContent;
            copyApiKeyBtn.querySelector('.button-text').textContent = '✅ 已复制';
            setTimeout(() => {
                copyApiKeyBtn.querySelector('.button-text').textContent = originalText;
            }, 2000);

            console.log('API Key已复制到剪贴板');
        } catch (err) {
            console.error('复制失败:', err);
            alert('复制失败，请手动选择并复制');
        }
    };

    // --- Event Listeners ---

    // 增强的数据加载逻辑
    const loadSettings = () => {
        console.log('🔄 加载扩展设置...');

        chrome.storage.local.get(null, async (data) => {
            if (chrome.runtime.lastError) {
                console.error('❌ 加载设置失败:', chrome.runtime.lastError);
                return;
            }

            console.log('📋 从存储加载的数据:', data);
            await updateUI(data);

            // 验证关键设置是否存在
            if (!data.apiUrl && !data.workerUrl) {
                console.warn('⚠️ 未找到Worker URL设置');
            }
            if (!data.apiKey && !data.token) {
                console.warn('⚠️ 未找到API Key设置');
            }
        });
    };

    // 初始加载
    loadSettings();

    // Listen for real-time changes from the background script
    chrome.storage.onChanged.addListener((changes, namespace) => {
        if (namespace === 'local') {
            // Reload all data to ensure UI is consistent
            chrome.storage.local.get(null, async (data) => {
                await updateUI(data);
            });
        }
    });

    // Handle "Sync Now" button click
    if (syncNowBtn) {
        syncNowBtn.addEventListener('click', () => {
            console.log('🚀 用户点击立即同步按钮');

            // Prevent multiple clicks
            if (syncNowBtn.disabled) {
                console.log('⚠️ 同步正在进行中，忽略重复点击');
                return;
            }

            // 设置按钮为加载状态
            setSyncButtonLoading(true);
            console.log('⏳ 开始同步...');

            // Send the message to background script
            chrome.runtime.sendMessage({ action: 'syncNow' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('❌ 发送同步请求失败:', chrome.runtime.lastError);
                    alert('同步请求失败: ' + chrome.runtime.lastError.message);
                    setSyncButtonLoading(false);
                } else {
                    console.log('✅ 同步请求已发送:', response);

                    // 显示成功反馈
                    syncNowBtnText.textContent = '✅ 同步中...';

                    // 监听同步状态变化，自动更新按钮状态
                    const checkSyncStatus = () => {
                        chrome.storage.local.get(['lastSyncStatus'], (data) => {
                            if (data.lastSyncStatus === 'syncing') {
                                // 仍在同步中，继续检查
                                setTimeout(checkSyncStatus, 1000);
                            } else {
                                // 同步完成，恢复按钮状态
                                setSyncButtonLoading(false);
                                console.log('🔄 同步完成，按钮状态已恢复');

                                // 更新UI显示最新状态
                                chrome.storage.local.get(null, async (latestData) => {
                                    await updateUI(latestData);
                                });
                            }
                        });
                    };

                    // 开始监听状态
                    setTimeout(checkSyncStatus, 1000);
                }
            });
        });
    }

    // 调试定时器状态按钮事件监听器
    if (debugAlarmBtn) {
        debugAlarmBtn.addEventListener('click', () => {
            console.log('🔍 请求检查定时器状态...');

            // 发送调试请求到后台脚本
            chrome.runtime.sendMessage({ action: 'debugAlarm' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('发送调试请求失败:', chrome.runtime.lastError);
                    alert('调试请求失败: ' + chrome.runtime.lastError.message);
                } else {
                    console.log('✅ 调试请求已发送，请查看控制台输出');
                    alert('定时器状态已输出到控制台\n\n请按F12打开开发者工具，查看Console标签页中的详细信息');
                }
            });
        });
    }

    // API Key 生成按钮事件监听器
    if (generateApiKeyBtn) {
        generateApiKeyBtn.addEventListener('click', () => {
            console.log('🎲 生成新的API Key...');

            const newApiKey = generateSecureApiKey();
            apiKeyInput.value = newApiKey;

            // 确保输入框处于可编辑状态以便用户看到变化
            setApiKeyEditMode(false);

            console.log('✅ 已生成新的API Key:', newApiKey.substring(0, 8) + '...');

            // 临时显示生成成功状态
            const originalText = generateApiKeyBtn.querySelector('.button-text').textContent;
            generateApiKeyBtn.querySelector('.button-text').textContent = '✅ 已生成';
            setTimeout(() => {
                generateApiKeyBtn.querySelector('.button-text').textContent = originalText;
            }, 2000);
        });
    }

    // API Key 编辑按钮事件监听器
    if (editApiKeyBtn) {
        let isEditing = false;

        editApiKeyBtn.addEventListener('click', () => {
            isEditing = !isEditing;
            setApiKeyEditMode(isEditing);

            if (!isEditing) {
                // 完成编辑时验证API Key
                const validation = validateApiKey(apiKeyInput.value.trim());
                if (!validation.valid) {
                    alert(validation.message);
                    isEditing = true;
                    setApiKeyEditMode(true);
                }
            }
        });
    }

    // API Key 复制按钮事件监听器
    if (copyApiKeyBtn) {
        copyApiKeyBtn.addEventListener('click', copyApiKeyToClipboard);
    }

    // 增强的设置保存逻辑
    if (saveSettingsBtn) {
        saveSettingsBtn.addEventListener('click', async () => {
            console.log('💾 开始保存设置...');

            const apiUrl = apiUrlInput.value.trim();
            const apiKey = apiKeyInput.value.trim();
            const syncInterval = parseInt(syncIntervalInput.value, 10);

            // 输入验证
            if (!apiUrl) {
                alert('请输入Worker URL');
                return;
            }

            if (!apiUrl.startsWith('https://')) {
                alert('Worker URL必须以https://开头');
                return;
            }

            // 验证API Key
            const apiKeyValidation = validateApiKey(apiKey);
            if (!apiKeyValidation.valid) {
                alert(apiKeyValidation.message);
                return;
            }

            if (syncInterval < 1 || syncInterval > 1440) {
                alert('同步频率必须在1-1440分钟之间（1分钟到24小时）');
                return;
            }

            // 检查是否是首次配置
            const existingSettings = await new Promise((resolve) => {
                chrome.storage.local.get(['apiUrl', 'apiKey', 'hasShownInitialPrompt'], (result) => {
                    resolve(result);
                });
            });

            const isFirstTimeSetup = !existingSettings.apiUrl || !existingSettings.apiKey;

            // 准备保存的设置数据
            const settings = {
                apiUrl: apiUrl,
                apiKey: apiKey,
                syncInterval: syncInterval,
                intervalUnit: 'minutes', // 标记使用分钟单位
                autoSync: true,
                // 添加时间戳和版本信息
                lastUpdated: new Date().toISOString(),
                version: '2.0.0'
            };

            // 如果是首次配置，标记为已显示提示（避免在quick-setup中重复显示）
            if (isFirstTimeSetup) {
                settings.hasShownInitialPrompt = true;
                console.log('🎉 首次配置，将标记为已显示提示');
            }

            console.log('📝 准备保存的设置:', {
                apiUrl: settings.apiUrl,
                apiKey: settings.apiKey ? '已设置' : '未设置',
                syncInterval: settings.syncInterval,
                autoSync: settings.autoSync
            });

            // 保存设置
            chrome.storage.local.set(settings, () => {
                if (chrome.runtime.lastError) {
                    console.error('❌ 保存设置失败:', chrome.runtime.lastError);
                    alert('保存设置失败: ' + chrome.runtime.lastError.message);
                    return;
                }

                console.log('✅ 设置保存成功');

                // 验证保存结果
                chrome.storage.local.get(['apiUrl', 'apiKey', 'syncInterval'], (savedData) => {
                    console.log('🔍 验证保存的数据:', savedData);

                    if (savedData.apiUrl === apiUrl && savedData.apiKey === apiKey) {
                        console.log('✅ 数据验证成功');
                    } else {
                        console.warn('⚠️ 数据验证失败');
                    }
                });

                // 通知后台脚本
                chrome.runtime.sendMessage({ action: 'updateSettings' }, (response) => {
                    if (chrome.runtime.lastError) {
                        console.warn('⚠️ 通知后台脚本失败:', chrome.runtime.lastError);
                    } else {
                        console.log('📢 已通知后台脚本');
                    }
                });

                // 显示保存成功消息
                if (settingsSavedMsg) {
                    settingsSavedMsg.classList.remove('hidden');
                    setTimeout(() => {
                        settingsSavedMsg.classList.add('hidden');
                    }, 2000);
                }
            });
        });
    }
});
