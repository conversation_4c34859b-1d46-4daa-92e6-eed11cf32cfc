#!/bin/bash

# Bing Rewards Cookie Sync Worker 部署脚本

echo "🚀 开始部署 Bing Rewards Cookie Sync Worker..."

# 检查当前目录
if [ ! -d "cloudflare-worker" ]; then
    echo "❌ 错误: 未找到 cloudflare-worker 目录"
    echo "请确保您在 bing-rewards-cookie-sync 根目录下运行此脚本"
    exit 1
fi

# 进入 cloudflare-worker 目录
cd cloudflare-worker

echo "📁 当前目录: $(pwd)"

# 检查必要文件
if [ ! -f "index.js" ]; then
    echo "❌ 错误: 未找到 index.js 文件"
    exit 1
fi

if [ ! -f "wrangler.toml" ]; then
    echo "❌ 错误: 未找到 wrangler.toml 文件"
    exit 1
fi

echo "✅ 文件检查通过"

# 检查 wrangler 登录状态
echo "🔍 检查 wrangler 登录状态..."
if ! wrangler whoami > /dev/null 2>&1; then
    echo "❌ 未登录 wrangler，请先登录:"
    echo "   wrangler login"
    exit 1
fi

echo "✅ wrangler 已登录"

# 显示配置信息
echo "📋 Worker 配置信息:"
cat wrangler.toml

# 询问是否继续部署
read -p "🤔 是否继续部署? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 部署已取消"
    exit 1
fi

# 部署 Worker
echo "🚀 开始部署..."
if wrangler deploy; then
    echo "✅ 部署成功!"
    echo ""
    echo "🎉 您的 Worker 已成功部署!"
    echo "📝 请记录您的 Worker URL 并在浏览器扩展中配置"
    echo ""
    echo "🔧 下一步:"
    echo "1. 复制上面显示的 Worker URL"
    echo "2. 在浏览器扩展设置中填入 Worker URL"
    echo "3. 设置您的自定义 TOKEN（6位以上字符）"
    echo "4. 测试同步功能"
    echo ""
    echo "💡 提示: 项目现在使用多用户模式，支持自定义TOKEN，无需设置AUTH_SECRET"
else
    echo "❌ 部署失败"
    echo ""
    echo "🔧 可能的解决方案:"
    echo "1. 检查网络连接"
    echo "2. 确认 wrangler 登录状态: wrangler whoami"
    echo "3. 检查 KV 命名空间是否存在: wrangler kv namespace list"
    echo "4. 如果 KV 不存在，创建新的: wrangler kv namespace create \"BING_KV\""
    exit 1
fi
