# Bing Rewards 自动化脚本 Docker Compose 配置
# 维护者: alxxxxla
# 版本: 2.0
# 支持跨平台部署 (Linux/Mac/Windows)

version: '3.8'

services:
  bing-rewards:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: bing-rewards-automation
    restart: unless-stopped
    
    # 环境变量配置
    environment:
      # 钉钉通知配置
      - DINGDING_WEBHOOK=${DINGDING_WEBHOOK:-https://oapi.dingtalk.com/robot/send?access_token=your_token_here}
      - DINGDING_SECRET=${DINGDING_SECRET:-your_secret_here}
      
      # Cookie 获取 URL 配置
      - COOKIE_URL=${COOKIE_URL:-https://bing-cookie-sync-worker.5s6.com/?token=<token>}
      
      # 搜索配置
      - PC_MAX_PER_HOUR=${PC_MAX_PER_HOUR:-4}
      - MOBILE_MAX_PER_HOUR=${MOBILE_MAX_PER_HOUR:-3}
      - PC_MAX_DAILY=${PC_MAX_DAILY:-90}
      - MOBILE_MAX_DAILY=${MOBILE_MAX_DAILY:-60}
      
      # 工作时间配置
      - WORKING_HOURS_START=${WORKING_HOURS_START:-6}
      - WORKING_HOURS_END=${WORKING_HOURS_END:-21}
      
      # 奖励任务执行时间（逗号分隔）
      - REWARD_EXECUTION_TIMES=${REWARD_EXECUTION_TIMES:-10,13,20}
      
      # 时区设置
      - TZ=Asia/Shanghai
      
      # 日志级别
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      
      # 运行模式（single: 单次运行, schedule: 定时运行）
      - RUN_MODE=${RUN_MODE:-single}
      
      # 定时运行间隔（分钟）
      - SCHEDULE_INTERVAL=${SCHEDULE_INTERVAL:-60}
    
    # 卷挂载（可选：用于持久化日志）
    volumes:
      - ./logs:/app/logs:rw
      - ./config:/app/config:ro
    
    # 网络配置
    networks:
      - bing-rewards-net
    
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M
    
    # 健康检查
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('https://www.bing.com', timeout=5)"]
      interval: 30m
      timeout: 10s
      retries: 3
      start_period: 30s
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  bing-rewards-net:
    driver: bridge

# 可选：添加定时任务服务
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: bing-rewards-scheduler
    restart: unless-stopped
    
    environment:
      - RUN_MODE=schedule
      - SCHEDULE_INTERVAL=${SCHEDULE_INTERVAL:-60}
      # 继承其他环境变量
      - DINGDING_WEBHOOK=${DINGDING_WEBHOOK}
      - DINGDING_SECRET=${DINGDING_SECRET}
      - COOKIE_URL=${COOKIE_URL}
      - TZ=Asia/Shanghai
    
    volumes:
      - ./logs:/app/logs:rw
      - ./config:/app/config:ro
    
    networks:
      - bing-rewards-net
    
    depends_on:
      - bing-rewards
    
    # 只在需要定时运行时启用
    profiles:
      - scheduler
