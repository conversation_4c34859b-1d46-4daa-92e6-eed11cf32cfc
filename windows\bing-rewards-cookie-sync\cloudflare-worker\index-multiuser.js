/**
 * Bing Rewards Cookie Sync - Multi-User Support
 * 支持多用户Cookie存储的Cloudflare Worker
 */

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);

    // 获取token参数
    const token = url.searchParams.get('token');
    if (!token) {
      return new Response('Unauthorized: Missing token parameter.', { 
        status: 401,
        headers: { 'Content-Type': 'text/plain' }
      });
    }

    // 验证token格式（基本验证）
    if (token.length < 6) {
      return new Response('Unauthorized: Invalid token format.', { 
        status: 401,
        headers: { 'Content-Type': 'text/plain' }
      });
    }

    // 使用token作为存储键的前缀，实现用户隔离
    const userCookieKey = `BING_COOKIE_${token}`;

    // 添加CORS头支持
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Content-Type': 'text/plain'
    };

    // 处理OPTIONS预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: corsHeaders
      });
    }

    switch (request.method) {
      case 'POST': {
        try {
          const cookieData = await request.text();
          
          if (!cookieData || cookieData.trim().length === 0) {
            return new Response('Cookie data is empty', { 
              status: 400,
              headers: corsHeaders
            });
          }

          // 存储Cookie数据，包含时间戳和元数据
          const cookieRecord = {
            data: cookieData,
            timestamp: new Date().toISOString(),
            userAgent: request.headers.get('User-Agent') || 'Unknown',
            ip: request.headers.get('CF-Connecting-IP') || 'Unknown'
          };

          await env.BING_KV.put(userCookieKey, JSON.stringify(cookieRecord));
          
          console.log(`Cookie saved for user: ${token.substring(0, 8)}...`);
          
          return new Response('Cookie data saved successfully', { 
            status: 200,
            headers: corsHeaders
          });
          
        } catch (error) {
          console.error('Error saving cookie data:', error);
          return new Response('Internal Server Error', { 
            status: 500,
            headers: corsHeaders
          });
        }
      }

      case 'GET': {
        try {
          const storedRecord = await env.BING_KV.get(userCookieKey);
          
          if (storedRecord === null) {
            return new Response('Cookie not found for this user.', { 
              status: 404,
              headers: corsHeaders
            });
          }

          // 尝试解析JSON格式的记录
          let cookieData;
          try {
            const record = JSON.parse(storedRecord);
            cookieData = record.data;
            
            // 可选：在响应头中包含元数据
            corsHeaders['X-Cookie-Timestamp'] = record.timestamp;
            corsHeaders['X-Cookie-Age'] = Math.floor((Date.now() - new Date(record.timestamp).getTime()) / 1000);
            
          } catch (parseError) {
            // 兼容旧格式（直接存储的Cookie字符串）
            cookieData = storedRecord;
          }

          console.log(`Cookie retrieved for user: ${token.substring(0, 8)}...`);
          
          return new Response(cookieData, {
            status: 200,
            headers: corsHeaders
          });
          
        } catch (error) {
          console.error('Error retrieving cookie data:', error);
          return new Response('Internal Server Error', { 
            status: 500,
            headers: corsHeaders
          });
        }
      }

      case 'DELETE': {
        // 新增：删除用户Cookie的功能
        try {
          await env.BING_KV.delete(userCookieKey);
          
          console.log(`Cookie deleted for user: ${token.substring(0, 8)}...`);
          
          return new Response('Cookie data deleted successfully', { 
            status: 200,
            headers: corsHeaders
          });
          
        } catch (error) {
          console.error('Error deleting cookie data:', error);
          return new Response('Internal Server Error', { 
            status: 500,
            headers: corsHeaders
          });
        }
      }

      default:
        return new Response('Method Not Allowed. Use POST to save, GET to retrieve, or DELETE to remove.', {
          status: 405,
          headers: {
            ...corsHeaders,
            'Allow': 'GET, POST, DELETE, OPTIONS'
          }
        });
    }
  },
};
