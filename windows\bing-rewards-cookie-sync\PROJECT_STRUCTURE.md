# 📁 项目结构说明

## 🏗️ 目录结构

```
bing-rewards-cookie-sync/
├── 📄 README.md                    # 项目主要说明文档
├── 📄 LICENSE                      # MIT 许可证
├── 📄 CHANGELOG.md                 # 版本更新日志
├── 📄 PROJECT_OVERVIEW.md          # 项目概述
├── 📄 PROJECT_STRUCTURE.md         # 本文件 - 项目结构说明
├── 📄 manifest.json                # Chrome/Edge 扩展清单文件
├── 📄 background.js                # 扩展后台脚本
├── 📄 popup.html                   # 扩展弹窗界面
├── 📄 package.json                 # Node.js 项目配置
├── 📄 package-lock.json            # 依赖锁定文件
├── 📄 deploy-worker.sh             # Cloudflare Worker 部署脚本
├── 📁 cloudflare-worker/           # Cloudflare Worker 相关文件
│   ├── 📄 index.js                 # Worker 主文件（多用户版本）
│   ├── 📄 index-advanced.js        # Worker 高级版本（可选）
│   └── 📄 wrangler.toml            # Cloudflare 部署配置
├── 📁 scripts/                     # 构建和工具脚本
│   ├── 📄 build.js                 # 扩展构建脚本
│   ├── 📄 deploy.sh                # 自托管部署脚本（可选）
│   ├── 📄 generate-icons.js        # 图标生成脚本
│   ├── 📄 popup.js                 # 弹窗界面逻辑
│   ├── 📄 quick-setup.js           # 快速设置工具
│   └── 📄 settings-diagnostic.js   # 设置诊断工具
├── 📁 styles/                      # 样式文件
│   └── 📄 popup.css                # 弹窗样式
├── 📁 icons/                       # 扩展图标
│   ├── 📄 icon.svg                 # 源图标（SVG格式）
│   ├── 📄 icon16.png               # 16x16 图标
│   ├── 📄 icon48.png               # 48x48 图标
│   └── 📄 icon128.png              # 128x128 图标
├── 📁 docs/                        # 文档目录
│   ├── 📄 COMPLETE-SETUP-GUIDE.md  # 完整配置指南
│   ├── 📄 MULTI-USER-GUIDE.md      # 多用户使用指南
│   ├── 📄 TOKEN-GUIDE.md           # TOKEN 使用说明
│   └── 📄 TROUBLESHOOTING.md       # 故障排除指南
└── 📁 node_modules/                # Node.js 依赖包（自动生成）
```

## 🔧 核心文件说明

### 浏览器扩展文件
- **manifest.json**: 扩展的配置文件，定义权限、图标、脚本等
- **background.js**: 后台服务脚本，处理 Cookie 获取和同步逻辑
- **popup.html**: 扩展弹窗的 HTML 结构
- **scripts/popup.js**: 弹窗的 JavaScript 逻辑
- **styles/popup.css**: 弹窗的样式定义

### Cloudflare Worker 文件
- **cloudflare-worker/index.js**: 主要的 Worker 代码（多用户版本）
- **cloudflare-worker/index-advanced.js**: 高级版本，包含管理员功能
- **cloudflare-worker/wrangler.toml**: Cloudflare 部署配置

### 构建和部署
- **scripts/build.js**: 构建扩展包的脚本
- **scripts/generate-icons.js**: 从 SVG 生成不同尺寸的 PNG 图标
- **deploy-worker.sh**: 一键部署 Cloudflare Worker 的脚本

### 工具和诊断
- **scripts/quick-setup.js**: 快速设置工具，帮助用户配置扩展
- **scripts/settings-diagnostic.js**: 设置诊断工具，排查配置问题

## 🚀 使用流程

### 1. 开发环境设置
```bash
npm install                    # 安装依赖
npm run generate-icons        # 生成图标
npm run build                 # 构建扩展
```

### 2. 部署 Cloudflare Worker
```bash
./deploy-worker.sh            # 一键部署
# 或手动部署：
cd cloudflare-worker
wrangler deploy
```

### 3. 安装浏览器扩展
1. 在 Edge 浏览器中打开 `edge://extensions/`
2. 开启开发者模式
3. 加载解压缩的扩展，选择项目根目录

### 4. 配置扩展
1. 点击扩展图标
2. 填入 Worker URL 和自定义 TOKEN
3. 设置同步频率
4. 保存设置并测试

## 📚 文档说明

- **README.md**: 项目主要说明，包含快速开始指南
- **docs/COMPLETE-SETUP-GUIDE.md**: 详细的配置步骤
- **docs/MULTI-USER-GUIDE.md**: 多用户功能使用指南
- **docs/TOKEN-GUIDE.md**: TOKEN 格式和安全建议
- **docs/TROUBLESHOOTING.md**: 常见问题和解决方案

## 🔄 版本管理

项目使用语义化版本控制：
- **主版本号**: 不兼容的 API 修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

当前版本：v2.0.0（多用户支持版本）

## 🛠️ 维护说明

### 定期维护任务
1. 更新依赖包：`npm update`
2. 检查安全漏洞：`npm audit`
3. 清理构建文件：`npm run clean`
4. 代码质量检查：`npm run lint`

### 发布新版本
1. 更新版本号：`package.json` 和 `manifest.json`
2. 更新 `CHANGELOG.md`
3. 构建和测试：`npm run build`
4. 提交代码并创建标签

## 📝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

维护者：[@alxxxxla](https://github.com/alxxxxla)
