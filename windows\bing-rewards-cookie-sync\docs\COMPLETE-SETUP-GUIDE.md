# 🚀 完整配置流程指南

## 📋 概述

本指南提供从Worker部署到扩展配置的完整步骤，确保您可以一次配置后长期使用Bing Rewards Cookie Sync。

## 🎯 配置目标

完成配置后，您将拥有：
- ✅ 正常运行的Cloudflare Worker
- ✅ 正确配置的浏览器扩展
- ✅ 持久化的设置（重新加载扩展后仍保持）
- ✅ 自动同步功能
- ✅ 手动同步功能

## 📝 前置准备

### 1. 账户准备
- [x] Cloudflare账户（免费版即可）
- [x] Microsoft账户（用于登录Bing）
- [x] Microsoft Edge浏览器

### 2. 工具准备
```bash
# 安装Wrangler CLI
npm install -g wrangler

# 验证安装
wrangler --version
```

### 3. 项目准备
```bash
# 确保项目文件完整
cd bing-rewards-cookie-sync
ls -la cloudflare-worker/
# 应该看到: index.js, wrangler.toml
```

## 🔧 第一步：部署Cloudflare Worker

### 1.1 登录Cloudflare
```bash
wrangler login
# 按提示在浏览器中完成登录
```

### 1.2 创建KV命名空间（如果需要）
```bash
# 检查现有KV命名空间
wrangler kv namespace list

# 如果没有BING_KV，创建新的
wrangler kv namespace create "BING_KV"
# 记录返回的ID，例如：********************************
```

### 1.3 配置wrangler.toml
```bash
cd cloudflare-worker
cat wrangler.toml
```

确保配置正确：
```toml
name = "bing-cookie-sync-worker"
main = "index.js"
compatibility_date = "2023-12-01"

[[kv_namespaces]]
binding = "BING_KV"
id = "您的KV命名空间ID"
```

### 1.4 设置认证密钥
```bash
# 设置Worker的全局认证密钥
wrangler secret put AUTH_SECRET
# 输入强密码，例如：my_secure_worker_key_2024
```

### 1.5 部署Worker
```bash
wrangler deploy
```

**成功输出示例**：
```
✅ Successfully published your Worker to
https://bing-cookie-sync-worker.alice123.workers.dev
```

**记录Worker URL**：`https://bing-cookie-sync-worker.alice123.workers.dev`

### 1.6 验证Worker部署
```bash
# 测试Worker响应
curl "https://your-worker-url.workers.dev?token=test"
# 应该返回：Unauthorized: Missing or incorrect token parameter.
```

## 🔧 第二步：安装浏览器扩展

### 2.1 加载扩展
1. 打开Edge浏览器
2. 访问 `edge://extensions/`
3. 开启"开发人员模式"
4. 点击"加载解压缩的扩展"
5. 选择 `bing-rewards-cookie-sync` 目录

### 2.2 验证扩展安装
- 工具栏应出现扩展图标
- 点击图标应显示弹窗界面
- 扩展图标可能显示"CFG"（需要配置）

## 🔧 第三步：配置扩展设置

### 3.1 打开设置界面
- 点击扩展图标
- 查看弹窗界面

### 3.2 填写配置信息

**Worker URL**：
```
https://bing-cookie-sync-worker.alice123.workers.dev
```
（替换为您在第一步获得的实际URL）

**API Key (TOKEN)**：
```
user_yourname_2024
```
（自定义您的个人TOKEN，参考[TOKEN指南](TOKEN-GUIDE.md)）

**同步频率**：
```
240
```
（1-1440分钟，建议240分钟即4小时）

### 3.3 保存设置
1. 点击"保存设置"按钮
2. 确认看到"已保存!"提示
3. 设置应该保持在输入框中

### 3.4 验证设置持久化
1. 关闭扩展弹窗
2. 重新点击扩展图标
3. 确认设置仍然显示在输入框中

**如果设置消失**：
- 右键扩展弹窗 → "检查"
- 在控制台运行：`runFullDiagnostic()`
- 查看诊断结果并按提示修复

## 🔧 第四步：登录Bing并测试

### 4.1 登录Bing账户
1. 访问 https://www.bing.com
2. 登录您的Microsoft账户
3. 访问 https://rewards.bing.com 确认能看到积分

### 4.2 执行首次同步
1. 点击扩展图标
2. 点击"立即同步"按钮
3. 观察扩展图标状态变化
4. 扩展图标应显示"OK"

### 4.3 验证同步结果
```bash
# 在浏览器中访问（替换为您的实际URL和TOKEN）
https://your-worker-url.workers.dev?token=your_token

# 应该返回Cookie字符串，例如：
# MUID=20C75A143E2B617F033A4EB13F51608C; SRCHD=AF=NOFORM...
```

## 🔧 第五步：验证自动同步

### 5.1 检查自动同步设置
- 扩展弹窗中确认"自动同步"已开启
- 同步频率设置正确

### 5.2 等待自动同步
- 等待设置的时间间隔
- 检查扩展弹窗中的"最后同步时间"
- 时间应该自动更新

## 🔧 第六步：长期使用验证

### 6.1 重新加载扩展测试
1. 访问 `edge://extensions/`
2. 找到扩展，点击"重新加载"
3. 点击扩展图标
4. 确认设置仍然保存
5. 执行"立即同步"测试

### 6.2 浏览器重启测试
1. 完全关闭Edge浏览器
2. 重新打开浏览器
3. 点击扩展图标
4. 确认设置仍然保存

### 6.3 系统重启测试
1. 重启计算机
2. 打开Edge浏览器
3. 点击扩展图标
4. 确认设置仍然保存

## 🔧 故障排除

### 问题1：设置无法保存
**症状**：点击"保存设置"后，重新打开弹窗设置消失

**解决方案**：
```javascript
// 在扩展弹窗控制台中运行
runFullDiagnostic()

// 如果诊断发现问题，手动保存设置
forceSaveSettings('https://your-worker-url.workers.dev', 'your_token')
```

### 问题2：同步失败
**症状**：扩展图标显示"ERR"

**检查清单**：
- [ ] Worker URL格式正确（https://开头）
- [ ] TOKEN格式正确（至少6个字符）
- [ ] 已登录Bing.com
- [ ] Worker正常运行

**测试Worker**：
```bash
curl "https://your-worker-url.workers.dev?token=your_token"
```

### 问题3：扩展图标显示"CFG"
**原因**：配置不完整

**解决方案**：
1. 检查Worker URL是否填写
2. 检查TOKEN是否填写
3. 重新保存设置

### 问题4：扩展图标显示"NO"
**原因**：未找到Bing Cookie

**解决方案**：
1. 访问 https://www.bing.com 并登录
2. 访问 https://rewards.bing.com
3. 执行手动同步

## 📊 配置验证清单

完成配置后，请确认以下项目：

### Cloudflare Worker
- [ ] Worker成功部署
- [ ] Worker URL可访问
- [ ] KV命名空间正确配置
- [ ] AUTH_SECRET已设置

### 浏览器扩展
- [ ] 扩展成功加载
- [ ] Worker URL已配置
- [ ] TOKEN已配置
- [ ] 设置可以持久化保存

### 功能测试
- [ ] 手动同步成功
- [ ] 自动同步正常
- [ ] Worker API返回Cookie数据
- [ ] 扩展图标显示"OK"

### 持久化测试
- [ ] 重新加载扩展后设置保持
- [ ] 重启浏览器后设置保持
- [ ] 重启系统后设置保持

## 🎉 配置完成

恭喜！您已成功配置Bing Rewards Cookie Sync。

### 日常使用
- 扩展会自动同步Cookie
- 可随时点击"立即同步"手动同步
- 通过Worker URL随时获取最新Cookie

### 维护建议
- 定期检查扩展状态
- 必要时更新TOKEN
- 监控Cloudflare使用量

### 获取支持
- 查看 [故障排除指南](TROUBLESHOOTING.md)
- 查看 [TOKEN使用指南](TOKEN-GUIDE.md)
- 查看 [多用户指南](MULTI-USER-GUIDE.md)

享受自动化的Bing Cookie管理体验！🚀
