<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="64" cy="64" r="60" fill="#0078d4" stroke="#106ebe" stroke-width="2"/>
  
  <!-- <PERSON>ie图标 -->
  <circle cx="64" cy="64" r="35" fill="#f4a261" stroke="#e76f51" stroke-width="2"/>
  
  <!-- <PERSON><PERSON>上的点点 -->
  <circle cx="55" cy="55" r="3" fill="#8b4513"/>
  <circle cx="70" cy="50" r="2.5" fill="#8b4513"/>
  <circle cx="58" cy="70" r="2" fill="#8b4513"/>
  <circle cx="75" cy="65" r="3" fill="#8b4513"/>
  <circle cx="50" cy="75" r="2" fill="#8b4513"/>
  <circle cx="72" cy="78" r="2.5" fill="#8b4513"/>
  
  <!-- 同步箭头 -->
  <g transform="translate(64, 64)">
    <!-- 顺时针箭头 -->
    <path d="M -20 -25 A 25 25 0 0 1 20 -25" stroke="white" stroke-width="3" fill="none" stroke-linecap="round"/>
    <polygon points="15,-30 25,-25 15,-20" fill="white"/>
    
    <!-- 逆时针箭头 -->
    <path d="M 20 25 A 25 25 0 0 1 -20 25" stroke="white" stroke-width="3" fill="none" stroke-linecap="round"/>
    <polygon points="-15,30 -25,25 -15,20" fill="white"/>
  </g>
  
  <!-- 数据库图标 -->
  <g transform="translate(90, 90)">
    <ellipse cx="0" cy="-5" rx="12" ry="4" fill="white"/>
    <rect x="-12" y="-5" width="24" height="10" fill="white"/>
    <ellipse cx="0" cy="5" rx="12" ry="4" fill="white"/>
  </g>
</svg>
