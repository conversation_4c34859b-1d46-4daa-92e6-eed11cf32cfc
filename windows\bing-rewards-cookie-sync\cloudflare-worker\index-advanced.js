/**
 * Bing Rewards Cookie Sync - Advanced Multi-User Support
 * 高级多用户支持，包含用户管理和统计功能
 */

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);

    // 获取参数
    const token = url.searchParams.get('token');
    const adminToken = url.searchParams.get('admin');
    const action = url.searchParams.get('action');

    // CORS头
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Content-Type': 'application/json'
    };

    // 处理OPTIONS预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, { status: 200, headers: corsHeaders });
    }

    // 管理员功能
    if (adminToken && adminToken === env.ADMIN_SECRET) {
      return await handleAdminRequest(request, env, action, corsHeaders);
    }

    // 普通用户功能
    if (!token) {
      return new Response(JSON.stringify({ 
        error: 'Missing token parameter',
        message: 'Please provide a valid token'
      }), { 
        status: 401,
        headers: corsHeaders
      });
    }

    // 验证token格式
    if (token.length < 6 || !/^[a-zA-Z0-9_-]+$/.test(token)) {
      return new Response(JSON.stringify({ 
        error: 'Invalid token format',
        message: 'Token must be at least 6 characters and contain only alphanumeric characters, underscores, and hyphens'
      }), { 
        status: 401,
        headers: corsHeaders
      });
    }

    const userCookieKey = `BING_COOKIE_${token}`;
    const userMetaKey = `USER_META_${token}`;

    switch (request.method) {
      case 'POST':
        return await handleCookieSave(request, env, userCookieKey, userMetaKey, token, corsHeaders);
      
      case 'GET':
        return await handleCookieRetrieve(env, userCookieKey, token, corsHeaders);
      
      case 'DELETE':
        return await handleCookieDelete(env, userCookieKey, userMetaKey, token, corsHeaders);
      
      default:
        return new Response(JSON.stringify({ 
          error: 'Method not allowed',
          allowed: ['GET', 'POST', 'DELETE']
        }), {
          status: 405,
          headers: { ...corsHeaders, 'Allow': 'GET, POST, DELETE, OPTIONS' }
        });
    }
  },
};

// 处理Cookie保存
async function handleCookieSave(request, env, userCookieKey, userMetaKey, token, corsHeaders) {
  try {
    const cookieData = await request.text();
    
    if (!cookieData || cookieData.trim().length === 0) {
      return new Response(JSON.stringify({ 
        error: 'Empty cookie data',
        message: 'Cookie data cannot be empty'
      }), { 
        status: 400,
        headers: corsHeaders
      });
    }

    const now = new Date();
    const cookieRecord = {
      data: cookieData,
      timestamp: now.toISOString(),
      userAgent: request.headers.get('User-Agent') || 'Unknown',
      ip: request.headers.get('CF-Connecting-IP') || 'Unknown',
      size: cookieData.length
    };

    // 更新用户元数据
    let userMeta = {};
    try {
      const existingMeta = await env.BING_KV.get(userMetaKey);
      if (existingMeta) {
        userMeta = JSON.parse(existingMeta);
      }
    } catch (e) {
      // 忽略解析错误，使用默认值
    }

    userMeta.lastUpdate = now.toISOString();
    userMeta.updateCount = (userMeta.updateCount || 0) + 1;
    userMeta.firstSeen = userMeta.firstSeen || now.toISOString();

    // 保存数据
    await Promise.all([
      env.BING_KV.put(userCookieKey, JSON.stringify(cookieRecord)),
      env.BING_KV.put(userMetaKey, JSON.stringify(userMeta))
    ]);

    console.log(`Cookie saved for user: ${token.substring(0, 8)}... (${cookieData.length} bytes)`);

    return new Response(JSON.stringify({ 
      success: true,
      message: 'Cookie data saved successfully',
      metadata: {
        size: cookieData.length,
        timestamp: now.toISOString(),
        updateCount: userMeta.updateCount
      }
    }), { 
      status: 200,
      headers: corsHeaders
    });

  } catch (error) {
    console.error('Error saving cookie data:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      message: 'Failed to save cookie data'
    }), { 
      status: 500,
      headers: corsHeaders
    });
  }
}

// 处理Cookie获取
async function handleCookieRetrieve(env, userCookieKey, token, corsHeaders) {
  try {
    const storedRecord = await env.BING_KV.get(userCookieKey);
    
    if (storedRecord === null) {
      return new Response(JSON.stringify({ 
        error: 'Cookie not found',
        message: 'No cookie data found for this user'
      }), { 
        status: 404,
        headers: corsHeaders
      });
    }

    let cookieData, metadata = {};
    try {
      const record = JSON.parse(storedRecord);
      cookieData = record.data;
      metadata = {
        timestamp: record.timestamp,
        age: Math.floor((Date.now() - new Date(record.timestamp).getTime()) / 1000),
        size: record.size || cookieData.length
      };
    } catch (parseError) {
      // 兼容旧格式
      cookieData = storedRecord;
      metadata.legacy = true;
    }

    console.log(`Cookie retrieved for user: ${token.substring(0, 8)}...`);

    // 根据Accept头决定返回格式
    const acceptHeader = corsHeaders['Accept'] || '';
    if (acceptHeader.includes('application/json')) {
      return new Response(JSON.stringify({ 
        success: true,
        data: cookieData,
        metadata
      }), {
        status: 200,
        headers: corsHeaders
      });
    } else {
      // 默认返回纯文本格式（向后兼容）
      return new Response(cookieData, {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'text/plain' }
      });
    }

  } catch (error) {
    console.error('Error retrieving cookie data:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      message: 'Failed to retrieve cookie data'
    }), { 
      status: 500,
      headers: corsHeaders
    });
  }
}

// 处理Cookie删除
async function handleCookieDelete(env, userCookieKey, userMetaKey, token, corsHeaders) {
  try {
    await Promise.all([
      env.BING_KV.delete(userCookieKey),
      env.BING_KV.delete(userMetaKey)
    ]);

    console.log(`Cookie deleted for user: ${token.substring(0, 8)}...`);

    return new Response(JSON.stringify({ 
      success: true,
      message: 'Cookie data deleted successfully'
    }), { 
      status: 200,
      headers: corsHeaders
    });

  } catch (error) {
    console.error('Error deleting cookie data:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error',
      message: 'Failed to delete cookie data'
    }), { 
      status: 500,
      headers: corsHeaders
    });
  }
}

// 处理管理员请求
async function handleAdminRequest(request, env, action, corsHeaders) {
  try {
    switch (action) {
      case 'list':
        return await listUsers(env, corsHeaders);
      case 'stats':
        return await getStats(env, corsHeaders);
      case 'cleanup':
        return await cleanupOldData(env, corsHeaders);
      default:
        return new Response(JSON.stringify({ 
          error: 'Invalid admin action',
          available: ['list', 'stats', 'cleanup']
        }), { 
          status: 400,
          headers: corsHeaders
        });
    }
  } catch (error) {
    console.error('Admin request error:', error);
    return new Response(JSON.stringify({ 
      error: 'Admin operation failed',
      message: error.message
    }), { 
      status: 500,
      headers: corsHeaders
    });
  }
}

// 列出所有用户
async function listUsers(env, corsHeaders) {
  // 注意：KV不支持列出所有键，这里提供一个简化的实现
  // 在实际使用中，可能需要维护一个用户列表
  return new Response(JSON.stringify({ 
    message: 'User listing requires additional implementation',
    note: 'KV storage does not support key enumeration'
  }), { 
    status: 200,
    headers: corsHeaders
  });
}

// 获取统计信息
async function getStats(env, corsHeaders) {
  return new Response(JSON.stringify({ 
    message: 'Statistics feature requires additional implementation',
    timestamp: new Date().toISOString()
  }), { 
    status: 200,
    headers: corsHeaders
  });
}

// 清理旧数据
async function cleanupOldData(env, corsHeaders) {
  return new Response(JSON.stringify({ 
    message: 'Cleanup feature requires additional implementation',
    timestamp: new Date().toISOString()
  }), { 
    status: 200,
    headers: corsHeaders
  });
}
