# 🔄 多用户Cookie存储实现指南

## 📋 概述

本指南详细说明如何将Bing Rewards Cookie Sync扩展升级为支持多用户的版本，实现不同用户的Cookie数据完全隔离存储。

## 🎯 功能特性

### ✅ 基础多用户功能
- **TOKEN隔离**：每个TOKEN对应独立的Cookie存储空间
- **数据隔离**：不同用户的Cookie数据完全分离，互不干扰
- **向后兼容**：保持原有API接口的兼容性
- **CORS支持**：支持跨域请求

### 🚀 高级功能（可选）
- **元数据记录**：记录Cookie更新时间、次数等信息
- **管理员接口**：提供用户管理和统计功能
- **数据清理**：支持删除过期或无用的Cookie数据
- **JSON响应**：支持结构化的API响应

## 🔧 实现方案

### 方案1：基础多用户支持（推荐）

**特点**：
- 简单易用，最小化修改
- 使用TOKEN作为存储键前缀
- 保持API简洁性

**存储结构**：
```
KV存储键格式：BING_COOKIE_{TOKEN}
例如：
- BING_COOKIE_user1_abc123
- BING_COOKIE_user2_def456
- BING_COOKIE_company_xyz789
```

**API调用示例**：
```bash
# 用户1保存Cookie
curl -X POST "https://worker.dev?token=user1_abc123" \
  -d "MUID=123; SRCHD=456..."

# 用户1获取Cookie
curl "https://worker.dev?token=user1_abc123"

# 用户2保存Cookie（完全独立）
curl -X POST "https://worker.dev?token=user2_def456" \
  -d "MUID=789; SRCHD=012..."
```

### 方案2：高级多用户管理

**特点**：
- 包含用户元数据管理
- 支持管理员功能
- 提供详细的API响应
- 支持数据统计和清理

**存储结构**：
```
Cookie数据：BING_COOKIE_{TOKEN}
用户元数据：USER_META_{TOKEN}
```

**管理员API示例**：
```bash
# 获取用户统计（需要管理员密钥）
curl "https://worker.dev?admin=admin_secret&action=stats"

# 清理过期数据
curl "https://worker.dev?admin=admin_secret&action=cleanup"
```

## 🚀 部署步骤

### 1. 选择实现方案

**基础版本**（推荐新手）：
```bash
cd cloudflare-worker
cp index-multiuser.js index.js
cp wrangler-multiuser.toml wrangler.toml
```

**高级版本**（需要管理功能）：
```bash
cd cloudflare-worker
cp index-advanced.js index.js
cp wrangler-multiuser.toml wrangler.toml
# 编辑wrangler.toml，将main改为index-advanced.js
```

### 2. 配置环境变量

**基础版本**：
无需额外配置，直接部署即可。

**高级版本**：
```bash
# 设置管理员密钥（可选）
wrangler secret put ADMIN_SECRET
# 输入您的管理员密钥，例如：admin_super_secret_2024
```

### 3. 部署Worker

```bash
# 部署到Cloudflare
wrangler deploy

# 部署成功后会显示Worker URL
# 例如：https://bing-cookie-sync-multiuser.your-account.workers.dev
```

### 4. 测试多用户功能

```bash
# 测试用户1
curl -X POST "https://your-worker.workers.dev?token=test_user_1" \
  -d "MUID=user1_cookie_data"

curl "https://your-worker.workers.dev?token=test_user_1"

# 测试用户2
curl -X POST "https://your-worker.workers.dev?token=test_user_2" \
  -d "MUID=user2_cookie_data"

curl "https://your-worker.workers.dev?token=test_user_2"

# 验证数据隔离：两个请求应返回不同的Cookie数据
```

## 🔐 TOKEN管理策略

### TOKEN格式建议

**个人用户**：
```
user_{username}_{random}
例如：user_alice_abc123, user_bob_def456
```

**企业用户**：
```
company_{name}_{department}_{id}
例如：company_acme_it_001, company_acme_hr_002
```

**项目用户**：
```
project_{name}_{env}_{id}
例如：project_webapp_prod_001, project_api_test_002
```

### TOKEN安全建议

1. **长度要求**：至少8个字符
2. **字符限制**：只使用字母、数字、下划线、连字符
3. **唯一性**：确保每个用户的TOKEN唯一
4. **保密性**：TOKEN应当作密钥保护，不要在日志中记录

## 📊 使用示例

### JavaScript集成

```javascript
class BingCookieClient {
  constructor(workerUrl, userToken) {
    this.workerUrl = workerUrl;
    this.userToken = userToken;
  }

  async saveCookies(cookieString) {
    const response = await fetch(`${this.workerUrl}?token=${this.userToken}`, {
      method: 'POST',
      body: cookieString
    });
    
    if (!response.ok) {
      throw new Error(`Save failed: ${await response.text()}`);
    }
    
    return await response.text();
  }

  async getCookies() {
    const response = await fetch(`${this.workerUrl}?token=${this.userToken}`);
    
    if (response.status === 404) {
      return null; // 没有找到Cookie
    }
    
    if (!response.ok) {
      throw new Error(`Get failed: ${await response.text()}`);
    }
    
    return await response.text();
  }

  async deleteCookies() {
    const response = await fetch(`${this.workerUrl}?token=${this.userToken}`, {
      method: 'DELETE'
    });
    
    if (!response.ok) {
      throw new Error(`Delete failed: ${await response.text()}`);
    }
    
    return await response.text();
  }
}

// 使用示例
const client = new BingCookieClient(
  'https://your-worker.workers.dev',
  'user_alice_abc123'
);

// 保存Cookie
await client.saveCookies('MUID=123; SRCHD=456...');

// 获取Cookie
const cookies = await client.getCookies();
console.log('用户Cookie:', cookies);
```

### Python集成

```python
import requests

class BingCookieClient:
    def __init__(self, worker_url, user_token):
        self.worker_url = worker_url
        self.user_token = user_token
        self.base_url = f"{worker_url}?token={user_token}"
    
    def save_cookies(self, cookie_string):
        response = requests.post(self.base_url, data=cookie_string)
        response.raise_for_status()
        return response.text
    
    def get_cookies(self):
        response = requests.get(self.base_url)
        if response.status_code == 404:
            return None
        response.raise_for_status()
        return response.text
    
    def delete_cookies(self):
        response = requests.delete(self.base_url)
        response.raise_for_status()
        return response.text

# 使用示例
client = BingCookieClient(
    'https://your-worker.workers.dev',
    'user_bob_def456'
)

# 保存和获取Cookie
client.save_cookies('MUID=789; SRCHD=012...')
cookies = client.get_cookies()
print(f"用户Cookie: {cookies}")
```

## 🔄 迁移现有数据

如果您已经有单用户版本的数据，可以通过以下步骤迁移：

### 1. 备份现有数据

```bash
# 获取现有Cookie数据
curl "https://old-worker.workers.dev?token=old_secret" > backup_cookies.txt
```

### 2. 迁移到新系统

```bash
# 将数据迁移到新的用户TOKEN下
curl -X POST "https://new-worker.workers.dev?token=migrated_user_001" \
  -d @backup_cookies.txt
```

## ⚠️ 注意事项

1. **KV存储限制**：
   - 免费版每天100,000次读取操作
   - 免费版每天1,000次写入操作
   - 单个值最大25MB

2. **TOKEN管理**：
   - 妥善保管所有用户的TOKEN
   - 建议建立TOKEN分发和管理机制
   - 定期轮换TOKEN以提高安全性

3. **成本考虑**：
   - 多用户会增加KV操作次数
   - 监控使用量，避免超出免费额度

4. **数据清理**：
   - 定期清理不活跃用户的数据
   - 实现数据过期机制

## 🎉 总结

通过以上实现，您的Bing Rewards Cookie Sync项目将支持：

- ✅ 完全的多用户数据隔离
- ✅ 灵活的TOKEN管理
- ✅ 向后兼容的API接口
- ✅ 可选的高级管理功能
- ✅ 简单的部署和维护

选择适合您需求的方案，按照步骤部署即可享受多用户Cookie存储功能！
