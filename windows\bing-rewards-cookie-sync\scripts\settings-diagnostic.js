// 设置持久化诊断工具

console.log('🔧 设置诊断工具已加载');

// 诊断存储功能
async function diagnoseStoage() {
    console.log('\n🔍 开始存储诊断...');
    
    // 1. 检查存储权限
    console.log('1️⃣ 检查存储权限...');
    if (typeof chrome !== 'undefined' && chrome.storage) {
        console.log('✅ chrome.storage API 可用');
    } else {
        console.error('❌ chrome.storage API 不可用');
        return;
    }
    
    // 2. 测试写入功能
    console.log('2️⃣ 测试写入功能...');
    const testData = {
        testKey: 'testValue',
        timestamp: new Date().toISOString()
    };
    
    try {
        await new Promise((resolve, reject) => {
            chrome.storage.local.set(testData, () => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve();
                }
            });
        });
        console.log('✅ 写入测试成功');
    } catch (error) {
        console.error('❌ 写入测试失败:', error);
        return;
    }
    
    // 3. 测试读取功能
    console.log('3️⃣ 测试读取功能...');
    try {
        const readData = await new Promise((resolve, reject) => {
            chrome.storage.local.get(['testKey'], (result) => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve(result);
                }
            });
        });
        
        if (readData.testKey === testData.testKey) {
            console.log('✅ 读取测试成功');
        } else {
            console.error('❌ 读取测试失败 - 数据不匹配');
        }
    } catch (error) {
        console.error('❌ 读取测试失败:', error);
        return;
    }
    
    // 4. 清理测试数据
    console.log('4️⃣ 清理测试数据...');
    try {
        await new Promise((resolve, reject) => {
            chrome.storage.local.remove(['testKey'], () => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve();
                }
            });
        });
        console.log('✅ 清理测试数据成功');
    } catch (error) {
        console.error('❌ 清理测试数据失败:', error);
    }
    
    console.log('🎉 存储诊断完成');
}

// 检查当前设置
async function checkCurrentSettings() {
    console.log('\n📋 检查当前设置...');
    
    try {
        const allData = await new Promise((resolve, reject) => {
            chrome.storage.local.get(null, (result) => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve(result);
                }
            });
        });
        
        console.log('📊 存储中的所有数据:', allData);
        
        // 检查关键设置
        const requiredKeys = ['apiUrl', 'apiKey', 'syncInterval'];
        const missingKeys = [];
        
        for (const key of requiredKeys) {
            if (!allData[key]) {
                missingKeys.push(key);
            }
        }
        
        if (missingKeys.length === 0) {
            console.log('✅ 所有必需设置都存在');
        } else {
            console.warn('⚠️ 缺少设置:', missingKeys);
        }
        
        // 检查设置格式
        if (allData.apiUrl && !allData.apiUrl.startsWith('https://')) {
            console.warn('⚠️ Worker URL格式可能不正确');
        }
        
        if (allData.apiKey && allData.apiKey.length < 6) {
            console.warn('⚠️ API Key长度可能不足');
        }
        
        return allData;
        
    } catch (error) {
        console.error('❌ 检查设置失败:', error);
        return null;
    }
}

// 强制保存设置
async function forceSaveSettings(apiUrl, apiKey, syncInterval = 4) {
    console.log('\n💾 强制保存设置...');
    
    const settings = {
        apiUrl: apiUrl,
        apiKey: apiKey,
        syncInterval: syncInterval,
        autoSync: true,
        lastUpdated: new Date().toISOString(),
        version: '2.0.0',
        forceSaved: true
    };
    
    try {
        await new Promise((resolve, reject) => {
            chrome.storage.local.set(settings, () => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve();
                }
            });
        });
        
        console.log('✅ 强制保存成功');
        
        // 验证保存结果
        const savedData = await checkCurrentSettings();
        if (savedData && savedData.apiUrl === apiUrl && savedData.apiKey === apiKey) {
            console.log('✅ 保存验证成功');
        } else {
            console.error('❌ 保存验证失败');
        }
        
    } catch (error) {
        console.error('❌ 强制保存失败:', error);
    }
}

// 清除所有设置
async function clearAllSettings() {
    console.log('\n🗑️ 清除所有设置...');
    
    try {
        await new Promise((resolve, reject) => {
            chrome.storage.local.clear(() => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve();
                }
            });
        });
        
        console.log('✅ 清除设置成功');
        
    } catch (error) {
        console.error('❌ 清除设置失败:', error);
    }
}

// 监听存储变化
function monitorStorageChanges() {
    console.log('\n👁️ 开始监听存储变化...');
    
    chrome.storage.onChanged.addListener((changes, namespace) => {
        console.log(`📢 存储变化 (${namespace}):`, changes);
        
        for (const [key, { oldValue, newValue }] of Object.entries(changes)) {
            console.log(`  ${key}: ${oldValue} → ${newValue}`);
        }
    });
    
    console.log('✅ 存储监听已启动');
}

// 完整诊断
async function runFullDiagnostic() {
    console.log('🚀 开始完整设置诊断...\n');
    
    // 1. 存储功能诊断
    await diagnoseStoage();
    
    // 2. 检查当前设置
    await checkCurrentSettings();
    
    // 3. 启动存储监听
    monitorStorageChanges();
    
    console.log('\n🏁 诊断完成');
    console.log('\n💡 可用命令:');
    console.log('- checkCurrentSettings() - 检查当前设置');
    console.log('- forceSaveSettings(url, key) - 强制保存设置');
    console.log('- clearAllSettings() - 清除所有设置');
    console.log('- diagnoseStoage() - 诊断存储功能');
}

// 导出函数到全局
if (typeof window !== 'undefined') {
    window.diagnoseStoage = diagnoseStoage;
    window.checkCurrentSettings = checkCurrentSettings;
    window.forceSaveSettings = forceSaveSettings;
    window.clearAllSettings = clearAllSettings;
    window.monitorStorageChanges = monitorStorageChanges;
    window.runFullDiagnostic = runFullDiagnostic;
}

// 自动运行诊断
if (typeof chrome !== 'undefined' && chrome.storage) {
    setTimeout(() => {
        console.log('💡 运行完整诊断: runFullDiagnostic()');
        runFullDiagnostic();
    }, 1000);
}
